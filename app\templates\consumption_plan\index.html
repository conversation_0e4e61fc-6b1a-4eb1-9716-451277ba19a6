{% extends 'base.html' %}

{% block title %}消耗计划管理{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* 专业化消耗计划管理页面样式 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 25px;
    border-radius: 8px;
}

.page-header h2 {
    margin: 0;
    font-weight: 600;
    font-size: 24px;
}

.page-header .subtitle {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

.stats-dashboard {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 25px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    text-align: center;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 13px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-planning { border-left: 4px solid #6c757d; }
.stat-approved { border-left: 4px solid #17a2b8; }
.stat-executed { border-left: 4px solid #28a745; }
.stat-cancelled { border-left: 4px solid #dc3545; }

.action-toolbar {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px 20px;
    margin-bottom: 20px;
}

.filter-section {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.data-table {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.table-professional {
    margin: 0;
    font-size: 15px;
}

.table-professional thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 12px 8px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-professional tbody td {
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table-professional tbody tr:hover {
    background-color: #f8f9fa;
}

.action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.btn-action {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.status-badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.id-badge {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
}

.date-display {
    font-weight: 500;
    color: #495057;
}

.meal-type {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.creator-info {
    color: #6c757d;
    font-size: 13px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.pagination-wrapper {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px;
    margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .action-toolbar {
        padding: 10px 15px;
    }

    .table-professional {
        font-size: 14px;
    }

    .table-professional thead th,
    .table-professional tbody td {
        padding: 8px 6px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 专业化页面头部 -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="fas fa-clipboard-list mr-2"></i>消耗计划管理</h2>
                    <p class="subtitle mb-0">统一管理食材消耗计划，优化库存配置，确保供应链高效运转</p>
                </div>
                <div class="col-md-4 text-right">
                    <div class="d-flex justify-content-end">
                        <a href="{{ url_for('consumption_plan.from_weekly_menu') }}" class="btn btn-light btn-sm mr-2">
                            <i class="fas fa-calendar-plus"></i> 从周菜单创建
                        </a>
                        <a href="{{ url_for('consumption_plan.new') }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-plus-circle"></i> 直接创建
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计仪表板 -->
    <div class="stats-dashboard">
        <div class="stats-grid">
            <div class="stat-card stat-planning">
                <div class="stat-number text-secondary">{{ plan_counts.planning or 0 }}</div>
                <div class="stat-label">计划中</div>
            </div>
            <div class="stat-card stat-approved">
                <div class="stat-number text-info">{{ plan_counts.approved or 0 }}</div>
                <div class="stat-label">已审核</div>
            </div>
            <div class="stat-card stat-executed">
                <div class="stat-number text-success">{{ plan_counts.executed or 0 }}</div>
                <div class="stat-label">已执行</div>
            </div>
            <div class="stat-card stat-cancelled">
                <div class="stat-number text-danger">{{ plan_counts.cancelled or 0 }}</div>
                <div class="stat-label">已取消</div>
            </div>
        </div>
    </div>

    <!-- 操作工具栏 -->
    <div class="action-toolbar">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 mr-3">消耗计划列表</h5>
                    <span class="badge badge-light">共 {{ pagination.total }} 条记录</span>
                </div>
            </div>
            <div class="col-md-4 text-right">
                <button type="button" class="btn btn-outline-primary btn-sm mr-2" data-toggle="collapse" data-target="#filterForm">
                    <i class="fas fa-filter"></i> 高级筛选
                </button>
                <a href="{{ url_for('stock_in.index') }}" class="btn btn-outline-secondary btn-sm mr-2">
                    <i class="fas fa-warehouse"></i> 入库管理
                </a>
                <a href="{{ url_for('stock_out.index') }}" class="btn btn-outline-info btn-sm">
                    <i class="fas fa-shipping-fast"></i> 出库管理
                </a>
            </div>
        </div>
    </div>

    <!-- 高级筛选区域 -->
    <div class="collapse" id="filterForm">
        <div class="filter-section">
            <form method="get" action="{{ url_for('consumption_plan.index') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label class="form-label">状态筛选</label>
                        <select name="status" class="form-control">
                            <option value="">全部状态</option>
                            <option value="计划中" {% if status == '计划中' %}selected{% endif %}>计划中</option>
                            <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                            <option value="已执行" {% if status == '已执行' %}selected{% endif %}>已执行</option>
                            <option value="已取消" {% if status == '已取消' %}selected{% endif %}>已取消</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">开始日期</label>
                        <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">结束日期</label>
                        <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex">
                            <button type="submit" class="btn btn-primary mr-2">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-undo"></i> 重置
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 专业化数据表格 -->
    <div class="data-table">
        <div class="table-responsive">
            <table class="table table-professional table-hover">
                <thead>
                    <tr>
                        <th style="width: 80px;">编号</th>
                        <th style="width: 120px;">消耗日期</th>
                        <th style="width: 100px;">餐次</th>
                        <th style="width: 100px;">预计人数</th>
                        <th style="width: 120px;">所属区域</th>
                        <th style="width: 100px;">状态</th>
                        <th style="width: 140px;">创建时间</th>
                        <th style="width: 100px;">创建人</th>
                        <th style="width: 200px;">操作</th>
                    </tr>
                </thead>
                        <tbody>
                            {% for consumption_plan in consumption_plans %}
                            <tr>
                                <td class="text-center">
                                    <small class="text-muted">#{{ consumption_plan.id }}</small>
                                </td>
                                <td>
                                    <small>
                                    {% if consumption_plan.consumption_date %}
                                        {{ consumption_plan.consumption_date|format_datetime('%m-%d') }}
                                    {% elif consumption_plan.menu_plan %}
                                        {{ consumption_plan.menu_plan.plan_date|format_datetime('%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <small>
                                    {% if consumption_plan.meal_type %}
                                        {{ consumption_plan.meal_type }}
                                    {% elif consumption_plan.menu_plan %}
                                        {{ consumption_plan.menu_plan.meal_type }}
                                    {% else %}
                                        -
                                    {% endif %}
                                    </small>
                                </td>
                                <td class="text-center">
                                    <small>
                                    {% if consumption_plan.menu_plan and consumption_plan.menu_plan.expected_diners %}
                                        {{ consumption_plan.menu_plan.expected_diners }}人
                                    {% else %}
                                        -
                                    {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <small>
                                    {% if consumption_plan.area_name %}
                                        {{ consumption_plan.area_name }}
                                    {% elif consumption_plan.menu_plan and consumption_plan.menu_plan.area and consumption_plan.menu_plan.area.name %}
                                        {{ consumption_plan.menu_plan.area.name }}
                                    {% else %}
                                        -
                                    {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <span class="badge badge-{{ consumption_plan.status|status_class }} badge-sm">
                                        {{ consumption_plan.status }}
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">{{ consumption_plan.created_at|format_datetime('%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    <small>{{ consumption_plan.creator.real_name or consumption_plan.creator.username }}</small>
                                </td>
                                <td>
                                    <div class="btn-group-compact">
                                        <a href="{{ url_for('consumption_plan.view', id=consumption_plan.id) }}"
                                           class="btn btn-xs btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        {% if consumption_plan.status == '计划中' %}
                                        <a href="{{ url_for('consumption_plan.edit', id=consumption_plan.id) }}"
                                           class="btn btn-xs btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-xs btn-outline-success" title="审核"
                                                onclick="approveConfirm('{{ url_for('consumption_plan.approve', id=consumption_plan.id) }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-outline-danger" title="取消"
                                                onclick="cancelConfirm('{{ url_for('consumption_plan.cancel', id=consumption_plan.id) }}')">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        {% endif %}

                                        {% if consumption_plan.status == '已审核' %}
                                        <button type="button" class="btn btn-xs btn-outline-warning" title="执行"
                                                onclick="executeConfirm('{{ url_for('consumption_plan.execute', id=consumption_plan.id) }}')">
                                            <i class="fas fa-dolly"></i>
                                        </button>
                                        <button type="button" class="btn btn-xs btn-outline-danger" title="取消"
                                                onclick="cancelConfirm('{{ url_for('consumption_plan.cancel', id=consumption_plan.id) }}')">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        {% endif %}

                                        {% if consumption_plan.status == '已执行' %}
                                        <a href="{{ url_for('traceability.interface') }}?trace_type=consumption_plan&trace_id={{ consumption_plan.id }}"
                                           class="btn btn-xs btn-outline-secondary" title="溯源">
                                            <i class="fas fa-search"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center">暂无消耗计划</td>
                            </tr>
                            {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 精简分页 -->
    {% if pagination.pages > 1 %}
    <div class="d-flex justify-content-center mt-3">
        <ul class="pagination pagination-sm">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('consumption_plan.index', page=pagination.prev_num, status=status, start_date=start_date, end_date=end_date) }}">
                    «
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">«</span>
            </li>
            {% endif %}

            {% for page in pagination.iter_pages() %}
                {% if page %}
                    {% if page != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('consumption_plan.index', page=page, status=status, start_date=start_date, end_date=end_date) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('consumption_plan.index', page=pagination.next_num, status=status, start_date=start_date, end_date=end_date) }}">
                    »
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">»</span>
            </li>
            {% endif %}
        </ul>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 确认审核
    function approveConfirm(url) {
        if (confirm('确定要审核该消耗计划吗？')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            document.body.appendChild(form);
            form.submit();
        }
    }

    // 确认执行
    function executeConfirm(url) {
        if (confirm('确定要执行该消耗计划吗？执行后将生成出库单。')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            document.body.appendChild(form);
            form.submit();
        }
    }

    // 确认取消
    function cancelConfirm(url) {
        if (confirm('确定要取消该消耗计划吗？此操作不可恢复。')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = url;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}
