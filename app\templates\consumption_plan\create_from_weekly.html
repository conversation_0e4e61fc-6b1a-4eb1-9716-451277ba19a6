{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">{{ title }}</h3>
        <div>
            <a href="{{ url_for('consumption_plan.from_weekly_menu') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回选择菜单
            </a>
        </div>
    </div>

    <!-- 周菜单信息 -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-calendar-week"></i> 周菜单信息
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>学校：</strong>{{ weekly_menu.area.name }}
                </div>
                <div class="col-md-3">
                    <strong>周期：</strong>{{ weekly_menu.week_start.strftime('%Y-%m-%d') }} 至 {{ weekly_menu.week_end.strftime('%Y-%m-%d') }}
                </div>
                <div class="col-md-3">
                    <strong>状态：</strong>
                    <span class="badge badge-success">{{ weekly_menu.status }}</span>
                </div>
                <div class="col-md-3">
                    <strong>仓库：</strong>{{ warehouse.name }}
                </div>
            </div>
        </div>
    </div>

    <!-- 创建消耗计划表单 -->
    <form method="post" action="{{ url_for('consumption_plan.process_weekly_creation') }}">
        <input type="hidden" name="weekly_menu_id" value="{{ weekly_menu.id }}">
        <input type="hidden" name="warehouse_id" value="{{ warehouse.id }}">
        
        <!-- 周菜单详情 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-utensils"></i> 选择要创建消耗计划的日期和餐次
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th style="width: 100px;">日期</th>
                                <th style="width: 80px;">星期</th>
                                <th>早餐</th>
                                <th>午餐</th>
                                <th>晚餐</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for date_str, day_data in week_data.items() %}
                            <tr>
                                <td>
                                    <strong>{{ date_str[5:] }}</strong>
                                </td>
                                <td>
                                    <span class="badge badge-secondary">{{ day_data.weekday }}</span>
                                </td>
                                
                                <!-- 早餐 -->
                                <td>
                                    {% if day_data.meals['早餐'] %}
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" 
                                               name="selected_meals" 
                                               value="{{ date_str }}_早餐"
                                               id="meal_{{ date_str }}_breakfast">
                                        <label class="form-check-label" for="meal_{{ date_str }}_breakfast">
                                            <strong>创建早餐消耗计划</strong>
                                        </label>
                                    </div>
                                    {% for recipe in day_data.meals['早餐'] %}
                                    <div class="recipe-item">
                                        <small class="text-primary">{{ recipe.name }}</small>
                                        {% if recipe.ingredients %}
                                        <div class="ingredients-list">
                                            {% for ingredient in recipe.ingredients %}
                                            <span class="badge badge-light">{{ ingredient.name }} {{ ingredient.quantity }}{{ ingredient.unit }}</span>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                    {% else %}
                                    <span class="text-muted">无菜单</span>
                                    {% endif %}
                                </td>
                                
                                <!-- 午餐 -->
                                <td>
                                    {% if day_data.meals['午餐'] %}
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" 
                                               name="selected_meals" 
                                               value="{{ date_str }}_午餐"
                                               id="meal_{{ date_str }}_lunch">
                                        <label class="form-check-label" for="meal_{{ date_str }}_lunch">
                                            <strong>创建午餐消耗计划</strong>
                                        </label>
                                    </div>
                                    {% for recipe in day_data.meals['午餐'] %}
                                    <div class="recipe-item">
                                        <small class="text-primary">{{ recipe.name }}</small>
                                        {% if recipe.ingredients %}
                                        <div class="ingredients-list">
                                            {% for ingredient in recipe.ingredients %}
                                            <span class="badge badge-light">{{ ingredient.name }} {{ ingredient.quantity }}{{ ingredient.unit }}</span>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                    {% else %}
                                    <span class="text-muted">无菜单</span>
                                    {% endif %}
                                </td>
                                
                                <!-- 晚餐 -->
                                <td>
                                    {% if day_data.meals['晚餐'] %}
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" 
                                               name="selected_meals" 
                                               value="{{ date_str }}_晚餐"
                                               id="meal_{{ date_str }}_dinner">
                                        <label class="form-check-label" for="meal_{{ date_str }}_dinner">
                                            <strong>创建晚餐消耗计划</strong>
                                        </label>
                                    </div>
                                    {% for recipe in day_data.meals['晚餐'] %}
                                    <div class="recipe-item">
                                        <small class="text-primary">{{ recipe.name }}</small>
                                        {% if recipe.ingredients %}
                                        <div class="ingredients-list">
                                            {% for ingredient in recipe.ingredients %}
                                            <span class="badge badge-light">{{ ingredient.name }} {{ ingredient.quantity }}{{ ingredient.unit }}</span>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endfor %}
                                    {% else %}
                                    <span class="text-muted">无菜单</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量选择按钮 -->
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                        <i class="fas fa-check-square"></i> 全选
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm ml-2" onclick="selectNone()">
                        <i class="fas fa-square"></i> 全不选
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm ml-2" onclick="selectLunchOnly()">
                        <i class="fas fa-sun"></i> 只选午餐
                    </button>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="mt-3 text-center">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-plus"></i> 创建选中的消耗计划
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
.recipe-item {
    margin-bottom: 8px;
    padding: 4px 0;
}

.ingredients-list {
    margin-top: 4px;
}

.ingredients-list .badge {
    margin-right: 4px;
    margin-bottom: 2px;
    font-size: 10px;
}

.form-check {
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.table td {
    vertical-align: top;
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function selectAll() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = true;
    });
}

function selectNone() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = false;
    });
}

function selectLunchOnly() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = checkbox.value.includes('_午餐');
    });
}
</script>
{% endblock %}
