{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">{{ title }}</h3>
        <div>
            <a href="{{ url_for('consumption_plan.select_weekly_menu') }}" class="btn btn-outline-info mr-2">
                <i class="fas fa-calendar-alt"></i> 选择其他周菜单
            </a>
            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回消耗计划
            </a>
        </div>
    </div>

    <!-- 周菜单信息 -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-calendar-week"></i> 周菜单信息
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>学校：</strong>{{ weekly_menu.area.name }}
                </div>
                <div class="col-md-3">
                    <strong>周期：</strong>{{ weekly_menu.week_start.strftime('%Y-%m-%d') }} 至 {{ weekly_menu.week_end.strftime('%Y-%m-%d') }}
                </div>
                <div class="col-md-3">
                    <strong>状态：</strong>
                    <span class="badge badge-success">{{ weekly_menu.status }}</span>
                </div>
                <div class="col-md-3">
                    <strong>仓库：</strong>{{ warehouse.name }}
                </div>
            </div>
        </div>
    </div>

    <!-- 创建消耗计划表单 -->
    <form method="post" action="{{ url_for('consumption_plan.process_weekly_creation') }}">
        <input type="hidden" name="weekly_menu_id" value="{{ weekly_menu.id }}">
        <input type="hidden" name="warehouse_id" value="{{ warehouse.id }}">

        <!-- 周菜单详情 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-utensils"></i> 选择要创建消耗计划的日期和餐次
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="thead-light">
                            <tr>
                                <th style="width: 100px;">日期</th>
                                <th style="width: 80px;">星期</th>
                                <th>早餐</th>
                                <th>午餐</th>
                                <th>晚餐</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for date_str, day_data in week_data.items() %}
                            <tr>
                                <td>
                                    <strong>{{ date_str[5:] }}</strong>
                                </td>
                                <td>
                                    <span class="badge badge-secondary">{{ day_data.weekday }}</span>
                                </td>

                                <!-- 早餐 -->
                                <td>
                                    {% if day_data.meals['早餐'] %}
                                    <div class="meal-container">
                                        {% set meal_key = date_str + '_早餐' %}
                                        {% if meal_key in existing_plans_map %}
                                        <!-- 已存在消耗计划 -->
                                        <div class="existing-plan-info">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">已有消耗计划</small>
                                                <span class="badge badge-{{ existing_plans_map[meal_key].status|status_class }} badge-sm">
                                                    {{ existing_plans_map[meal_key].status }}
                                                </span>
                                            </div>
                                            <a href="{{ url_for('consumption_plan.view', id=existing_plans_map[meal_key].id) }}"
                                               class="btn btn-outline-info btn-xs mt-1" target="_blank">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                        </div>
                                        <hr class="my-2">
                                        {% endif %}

                                        <div class="meal-header">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="selected_meals"
                                                       value="{{ date_str }}_早餐"
                                                       id="meal_{{ date_str }}_breakfast">
                                                <label class="form-check-label" for="meal_{{ date_str }}_breakfast">
                                                    <strong>
                                                        {% if meal_key in existing_plans_map %}
                                                        再次创建早餐消耗计划
                                                        {% else %}
                                                        创建早餐消耗计划
                                                        {% endif %}
                                                    </strong>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="recipes-grid">
                                            {% for recipe in day_data.meals['早餐'] %}
                                            <div class="recipe-item">
                                                <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">无菜单</span>
                                    {% endif %}
                                </td>

                                <!-- 午餐 -->
                                <td>
                                    {% if day_data.meals['午餐'] %}
                                    <div class="meal-container">
                                        {% set meal_key = date_str + '_午餐' %}
                                        {% if meal_key in existing_plans_map %}
                                        <!-- 已存在消耗计划 -->
                                        <div class="existing-plan-info">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">已有消耗计划</small>
                                                <span class="badge badge-{{ existing_plans_map[meal_key].status|status_class }} badge-sm">
                                                    {{ existing_plans_map[meal_key].status }}
                                                </span>
                                            </div>
                                            <a href="{{ url_for('consumption_plan.view', id=existing_plans_map[meal_key].id) }}"
                                               class="btn btn-outline-info btn-xs mt-1" target="_blank">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                        </div>
                                        <hr class="my-2">
                                        {% endif %}

                                        <div class="meal-header">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="selected_meals"
                                                       value="{{ date_str }}_午餐"
                                                       id="meal_{{ date_str }}_lunch">
                                                <label class="form-check-label" for="meal_{{ date_str }}_lunch">
                                                    <strong>
                                                        {% if meal_key in existing_plans_map %}
                                                        再次创建午餐消耗计划
                                                        {% else %}
                                                        创建午餐消耗计划
                                                        {% endif %}
                                                    </strong>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="recipes-grid">
                                            {% for recipe in day_data.meals['午餐'] %}
                                            <div class="recipe-item">
                                                <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">无菜单</span>
                                    {% endif %}
                                </td>

                                <!-- 晚餐 -->
                                <td>
                                    {% if day_data.meals['晚餐'] %}
                                    <div class="meal-container">
                                        {% set meal_key = date_str + '_晚餐' %}
                                        {% if meal_key in existing_plans_map %}
                                        <!-- 已存在消耗计划 -->
                                        <div class="existing-plan-info">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-muted">已有消耗计划</small>
                                                <span class="badge badge-{{ existing_plans_map[meal_key].status|status_class }} badge-sm">
                                                    {{ existing_plans_map[meal_key].status }}
                                                </span>
                                            </div>
                                            <a href="{{ url_for('consumption_plan.view', id=existing_plans_map[meal_key].id) }}"
                                               class="btn btn-outline-info btn-xs mt-1" target="_blank">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                        </div>
                                        <hr class="my-2">
                                        {% endif %}

                                        <div class="meal-header">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="selected_meals"
                                                       value="{{ date_str }}_晚餐"
                                                       id="meal_{{ date_str }}_dinner">
                                                <label class="form-check-label" for="meal_{{ date_str }}_dinner">
                                                    <strong>
                                                        {% if meal_key in existing_plans_map %}
                                                        再次创建晚餐消耗计划
                                                        {% else %}
                                                        创建晚餐消耗计划
                                                        {% endif %}
                                                    </strong>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="recipes-grid">
                                            {% for recipe in day_data.meals['晚餐'] %}
                                            <div class="recipe-item">
                                                <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">无菜单</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 批量选择按钮 -->
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                        <i class="fas fa-check-square"></i> 全选
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm ml-2" onclick="selectNone()">
                        <i class="fas fa-square"></i> 全不选
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm ml-2" onclick="selectLunchOnly()">
                        <i class="fas fa-sun"></i> 只选午餐
                    </button>
                </div>
            </div>
        </div>

        <!-- 提交按钮 -->
        <div class="mt-3 text-center">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-plus"></i> 创建选中的消耗计划
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* 餐次容器 */
.meal-container {
    width: 100%;
}

/* 已存在消耗计划信息 */
.existing-plan-info {
    background-color: #f8f9fa;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid #17a2b8;
    margin-bottom: 8px;
}

.btn-xs {
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1.2;
}

/* 餐次标题区域 */
.meal-header {
    margin-bottom: 8px;
}

.form-check {
    background-color: #f8f9fa;
    padding: 6px 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    margin-bottom: 0;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    font-size: 14px;
}

/* 菜谱网格布局 - 两列显示 */
.recipes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px 10px;
    margin-top: 10px;
}

.recipe-item {
    padding: 4px 0;
    font-size: 14px;
    color: #495057;
    line-height: 1.4;
    word-break: break-word;
}

.recipe-item i {
    margin-right: 4px;
    font-size: 12px;
    color: #6c757d;
}

/* 表格样式 */
.table td {
    vertical-align: top;
    padding: 10px 6px;
    min-width: 180px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    text-align: center;
    padding: 8px 6px;
}

/* 让表格更紧凑 */
.table-responsive {
    font-size: 14px;
}

/* 优化复选框样式 */
.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* 让日期列更突出 */
.table td:first-child {
    font-weight: 600;
    color: #495057;
    text-align: center;
    min-width: 80px;
}

/* 星期列样式 */
.table td:nth-child(2) {
    text-align: center;
    min-width: 60px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .recipes-grid {
        grid-template-columns: 1fr;
        gap: 4px;
    }

    .table td {
        min-width: 120px;
        padding: 8px 4px;
    }

    .recipe-item {
        font-size: 13px;
    }

    .form-check-label {
        font-size: 13px;
    }
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function selectAll() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = true;
    });
}

function selectNone() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = false;
    });
}

function selectLunchOnly() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = checkbox.value.includes('_午餐');
    });
}
</script>
{% endblock %}
