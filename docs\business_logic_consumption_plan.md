# 消耗计划管理系统 - 业务逻辑说明

## 📋 概述

消耗计划管理系统是学校食堂管理系统的核心模块，负责根据周菜单创建和管理食材消耗计划。系统采用严格的学校级数据隔离和权限控制机制。

## 🏫 数据隔离和权限控制

### 核心原则
- **学校级数据隔离**：每个学校的数据完全独立，不能跨学校访问
- **用户权限绑定**：每个用户只能操作其关联学校的数据
- **严格权限检查**：所有操作都需要通过权限验证

### 权限检查机制
```python
# 1. 用户必须关联到学校
if not current_user.area_id:
    flash('您没有关联任何学校，无法创建消耗计划', 'danger')
    return redirect(url_for('consumption_plan.index'))

# 2. 检查用户是否有权限访问目标学校
if not current_user.can_access_area_by_id(weekly_menu.area_id):
    flash('您没有权限操作该区域', 'danger')
    return redirect(url_for('consumption_plan.from_weekly_menu'))
```

### 数据范围限制
所有数据查询都限制在用户所属学校范围内：
- **周菜单**：`area_id = current_user.area_id`
- **消耗计划**：`area_id = weekly_menu.area_id`
- **仓库**：`area_id = weekly_menu.area_id`
- **库存**：通过仓库关联限制到学校

## 🔄 消耗计划创建流程

### 1. 选择周菜单阶段
- 只显示用户所属学校的已发布周菜单
- 显示每个周菜单的消耗计划统计信息
- 支持为同一周创建多个消耗计划（不同日期/餐次）

### 2. 创建消耗计划阶段
- 基于选定的周菜单显示一周的餐次安排
- 只有安排了菜谱的餐次才显示选择框
- 无菜谱的餐次显示"无菜单安排"提示

### 3. 智能重复检测
- 基于主材相似度分析（排除调味料）
- 使用Jaccard相似度算法（交集/并集）
- 30%相似度阈值作为警告标准
- 提供警告但不阻止创建

## 🧮 相似度分析算法

### 主材提取
```python
# 只考虑非调味料的食材
if detail.ingredient and not detail.ingredient.is_condiment:
    plan_ingredients.add(detail.ingredient_id)
```

### 相似度计算
```python
def calculate_ingredient_similarity(ingredients1, ingredients2):
    intersection = ingredients1.intersection(ingredients2)  # 交集
    union = ingredients1.union(ingredients2)                # 并集
    similarity = len(intersection) / len(union)             # Jaccard系数
    return similarity
```

### 相似度分类
- **高相似度（≥30%）**：显示黄色警告，标记"可能重复"
- **已有但不同（<30%）**：显示蓝色信息，标记"主材不同"
- **全新创建（无已有）**：正常创建流程

## 📊 数据结构设计

### 消耗计划表（consumption_plans）
- `area_id`：关联学校ID（核心隔离字段）
- `consumption_date`：消耗日期
- `meal_type`：餐次类型（早餐/午餐/晚餐）
- `status`：状态（计划中/已审核/已执行/已取消）
- `creator_id`：创建者ID

### 消耗明细表（consumption_details）
- `consumption_plan_id`：关联消耗计划
- `ingredient_id`：食材ID
- `planned_quantity`：计划消耗量
- `unit`：单位

## 🎯 用户界面设计

### 专业化显示
- 有菜谱的餐次：显示选择框、相似度分析、菜谱列表
- 无菜谱的餐次：显示"无菜单安排"提示，不显示选择框
- 两列网格布局：菜谱名称分两列显示，节省空间

### 相似度警告
- **警告样式**：黄色背景，警告图标
- **信息显示**：具体相似度百分比
- **快速链接**：查看相似消耗计划的链接
- **非阻塞性**：提供警告但允许继续创建

## 🔒 安全考虑

### 数据安全
- 严格的学校级数据隔离
- 所有数据库查询都包含学校ID过滤
- 用户无法访问其他学校的任何数据

### 操作安全
- 多层权限检查机制
- 状态验证（只能编辑"计划中"状态的消耗计划）
- 事务回滚机制确保数据一致性

### 业务安全
- 智能重复检测避免资源浪费
- 库存检查确保食材供应充足
- 审计日志记录所有关键操作

## 📈 性能优化

### 数据库优化
- 使用索引优化查询性能（area_id, consumption_date, meal_type）
- 批量查询减少数据库访问次数
- 原始SQL查询处理复杂统计需求

### 前端优化
- 两列网格布局提高空间利用率
- 14px字体确保可读性
- 响应式设计适配不同设备

## 🔄 业务流程

### 标准流程
1. **用户登录** → 验证学校关联
2. **选择周菜单** → 显示本校已发布菜单
3. **分析相似度** → 智能检测重复风险
4. **选择餐次** → 只显示有菜谱的餐次
5. **创建计划** → 生成消耗计划和明细
6. **库存检查** → 验证食材供应能力

### 异常处理
- 无学校关联：引导用户联系管理员
- 无权限访问：重定向到有权限的页面
- 无可用仓库：提示创建仓库
- 创建失败：事务回滚并显示错误信息

## 📝 注意事项

### 开发注意
- 所有涉及学校数据的查询必须包含area_id过滤
- 权限检查必须在数据操作之前进行
- 相似度分析只考虑主材，排除调味料
- 界面设计要考虑专业性和实用性

### 运维注意
- 定期检查数据隔离的完整性
- 监控相似度分析的准确性
- 关注用户权限配置的正确性
- 备份和恢复要考虑学校级别的数据完整性

---

**最后更新**：2025年1月
**版本**：v1.0
**维护者**：系统开发团队
