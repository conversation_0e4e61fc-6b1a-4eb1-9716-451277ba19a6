{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">{{ title }}</h3>
        <div>
            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回消耗计划
            </a>
        </div>
    </div>

    <!-- 区域筛选 -->
    <div class="card mb-3">
        <div class="card-body">
            <form method="get" action="{{ url_for('consumption_plan.from_weekly_menu') }}">
                <div class="row align-items-end">
                    <div class="col-md-4">
                        <label for="area_id" class="form-label">选择学校</label>
                        <select name="area_id" id="area_id" class="form-control" onchange="this.form.submit()">
                            <option value="">全部学校</option>
                            {% for area in areas %}
                            <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>
                                {{ area.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-8">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> 
                            只显示已发布状态的周菜单，可以用来创建消耗计划
                        </small>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 周菜单列表 -->
    {% if weekly_menus %}
    <div class="row">
        {% for menu in weekly_menus %}
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card h-100 {% if menu.has_consumption_plans %}border-warning{% else %}border-primary{% endif %}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-calendar-week"></i> {{ menu.week_display }}
                    </h6>
                    {% if menu.has_consumption_plans %}
                    <span class="badge badge-warning">已有消耗计划</span>
                    {% else %}
                    <span class="badge badge-success">可创建</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>学校：</strong>{{ menu.area.name }}
                    </div>
                    <div class="mb-2">
                        <strong>状态：</strong>
                        <span class="badge badge-success">{{ menu.status }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>创建时间：</strong>
                        <small class="text-muted">{{ menu.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    <div class="mb-3">
                        <strong>创建人：</strong>
                        <small>{{ menu.creator.real_name or menu.creator.username }}</small>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('weekly_menu_v2.view', id=menu.id) }}" 
                           class="btn btn-outline-info btn-sm" target="_blank">
                            <i class="fas fa-eye"></i> 查看菜单
                        </a>
                        
                        {% if menu.has_consumption_plans %}
                        <button class="btn btn-warning btn-sm" 
                                onclick="confirmCreateWithExisting('{{ url_for('consumption_plan.create_from_weekly', weekly_menu_id=menu.id) }}')">
                            <i class="fas fa-exclamation-triangle"></i> 仍要创建
                        </button>
                        {% else %}
                        <a href="{{ url_for('consumption_plan.create_from_weekly', weekly_menu_id=menu.id) }}" 
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 创建消耗计划
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center">
            <div class="py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无可用的周菜单</h5>
                <p class="text-muted">
                    {% if area_id %}
                    该学校暂无已发布的周菜单，请先创建并发布周菜单。
                    {% else %}
                    暂无已发布的周菜单，请先创建并发布周菜单。
                    {% endif %}
                </p>
                <div class="mt-3">
                    <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-primary">
                        <i class="fas fa-calendar-plus"></i> 去创建周菜单
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function confirmCreateWithExisting(url) {
    if (confirm('该周已经有消耗计划了，确定要继续创建新的消耗计划吗？\n\n注意：这可能会导致重复的消耗计划。')) {
        window.location.href = url;
    }
}
</script>
{% endblock %}
