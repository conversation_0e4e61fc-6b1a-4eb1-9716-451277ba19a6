{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="mb-0">{{ title }}</h3>
        <div>
            <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回消耗计划
            </a>
        </div>
    </div>

    <!-- 当前学校信息 -->
    <div class="card mb-3">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">
                        <i class="fas fa-school"></i> {{ current_area.name }}
                    </h5>
                    <small class="text-muted">当前学校的已发布周菜单</small>
                </div>
                <div class="col-md-6 text-right">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        选择周菜单来创建对应的消耗计划
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- 周菜单列表 -->
    {% if weekly_menus %}
    <div class="row">
        {% for menu in weekly_menus %}
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card h-100 {% if menu.has_consumption_plans %}border-warning{% else %}border-primary{% endif %}">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-calendar-week"></i> {{ menu.week_display }}
                        </h6>
                        <span class="badge badge-primary">{{ menu.consumption_plans_count }} 个消耗计划</span>
                    </div>
                    {% if menu.has_consumption_plans %}
                    <div class="mt-2">
                        <small class="text-muted">状态分布：</small>
                        {% if menu.plans_by_status['计划中'] > 0 %}
                        <span class="badge badge-secondary badge-sm">计划中 {{ menu.plans_by_status['计划中'] }}</span>
                        {% endif %}
                        {% if menu.plans_by_status['已审核'] > 0 %}
                        <span class="badge badge-info badge-sm">已审核 {{ menu.plans_by_status['已审核'] }}</span>
                        {% endif %}
                        {% if menu.plans_by_status['已执行'] > 0 %}
                        <span class="badge badge-success badge-sm">已执行 {{ menu.plans_by_status['已执行'] }}</span>
                        {% endif %}
                        {% if menu.plans_by_status['已取消'] > 0 %}
                        <span class="badge badge-danger badge-sm">已取消 {{ menu.plans_by_status['已取消'] }}</span>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>学校：</strong>{{ menu.area.name }}
                    </div>
                    <div class="mb-2">
                        <strong>状态：</strong>
                        <span class="badge badge-success">{{ menu.status }}</span>
                    </div>
                    <div class="mb-2">
                        <strong>创建时间：</strong>
                        <small class="text-muted">{{ menu.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                    </div>
                    <div class="mb-3">
                        <strong>创建人：</strong>
                        <small>{{ menu.creator.real_name or menu.creator.username }}</small>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('weekly_menu_v2.view', id=menu.id) }}"
                           class="btn btn-outline-info btn-sm" target="_blank">
                            <i class="fas fa-eye"></i> 查看菜单
                        </a>

                        <a href="{{ url_for('consumption_plan.create_from_weekly', weekly_menu_id=menu.id) }}"
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i>
                            {% if menu.has_consumption_plans %}
                            继续创建消耗计划
                            {% else %}
                            创建消耗计划
                            {% endif %}
                        </a>
                    </div>

                    {% if menu.has_consumption_plans %}
                    <div class="mt-2">
                        <small class="text-info">
                            <i class="fas fa-info-circle"></i>
                            可以为同一周创建多个消耗计划（不同日期/餐次）
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center">
            <div class="py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无可用的周菜单</h5>
                <p class="text-muted">
                    {{ current_area.name }} 暂无已发布的周菜单，请先创建并发布周菜单。
                </p>
                <div class="mt-3">
                    <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-primary">
                        <i class="fas fa-calendar-plus"></i> 去创建周菜单
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% endblock %}
