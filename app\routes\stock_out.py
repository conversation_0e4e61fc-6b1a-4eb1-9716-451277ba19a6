from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import StockOut, StockOutItem, Warehouse, Inventory, ConsumptionPlan, MenuPlan, MenuRecipe, Ingredient, StorageLocation
from app.utils.school_required import school_required
from app import db
from datetime import datetime, date
import json
from sqlalchemy import text

stock_out_bp = Blueprint('stock_out', __name__)

def get_stock_out_ingredient_traceability(stock_out, stock_out_items):
    """获取出库单食材溯源信息 - 一步一步来：先读消耗日期和餐次的菜谱，再通过食材名字比对"""
    from app.models import MenuRecipe, Recipe, RecipeIngredient, Supplier, MenuPlan, WeeklyMenu, WeeklyMenuRecipe
    from sqlalchemy import text

    try:
        traceability_data = []

        # 如果出库单关联了消耗计划，获取详细的溯源信息
        if stock_out.consumption_plan_id:
            consumption_plan = ConsumptionPlan.query.get(stock_out.consumption_plan_id)
            if consumption_plan:
                consumption_date = consumption_plan.consumption_date
                meal_type = consumption_plan.meal_type

                current_app.logger.info(f"步骤1: 读取消耗日期: {consumption_date}, 餐次: {meal_type}")

                # 步骤1: 先读出消耗日期和餐次的菜谱
                recipes_for_meal = []

                # 1.1 从菜单计划中读取菜谱
                if consumption_plan.menu_plan_id:
                    menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)
                    if menu_plan:
                        menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=menu_plan.id).all()
                        for menu_recipe in menu_recipes:
                            if menu_recipe.recipe:
                                recipes_for_meal.append({
                                    'recipe': menu_recipe.recipe,
                                    'planned_quantity': menu_recipe.planned_quantity,
                                    'source': 'menu_plan'
                                })

                # 1.2 如果菜单计划没有菜谱，从周菜单中读取
                if not recipes_for_meal and consumption_plan.menu_plan_id:
                    menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)
                    if menu_plan and menu_plan.area_id:
                        # 查找周菜单
                        weekday = consumption_date.weekday()  # 0-6，0表示周一
                        weekly_menu = WeeklyMenu.query.filter(
                            WeeklyMenu.week_start <= consumption_date,
                            WeeklyMenu.week_end >= consumption_date,
                            WeeklyMenu.area_id == menu_plan.area_id,
                            WeeklyMenu.status == '已发布'
                        ).first()

                        if weekly_menu:
                            weekly_recipes = WeeklyMenuRecipe.query.filter(
                                WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                                WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
                                WeeklyMenuRecipe.meal_type == meal_type
                            ).all()

                            for weekly_recipe in weekly_recipes:
                                if weekly_recipe.recipe:
                                    recipes_for_meal.append({
                                        'recipe': weekly_recipe.recipe,
                                        'planned_quantity': None,
                                        'source': 'weekly_menu'
                                    })

                current_app.logger.info(f"步骤1完成: 找到 {len(recipes_for_meal)} 个菜谱")
                for recipe_info in recipes_for_meal:
                    current_app.logger.info(f"  - 菜谱: {recipe_info['recipe'].name}")

                # 步骤2: 为每个出库明细查找对应的菜谱（通过食材名字比对）
                for item in stock_out_items:
                    ingredient_name = item.ingredient.name
                    current_app.logger.info(f"步骤2: 为出库食材 '{ingredient_name}' 查找对应菜谱")

                    # 步骤3: 遍历所有菜谱，检查是否含有该食材
                    for recipe_info in recipes_for_meal:
                        recipe = recipe_info['recipe']

                        # 通过食材名字比对（检查该食谱是否使用了该食材）
                        recipe_ingredient = RecipeIngredient.query.join(Ingredient).filter(
                            RecipeIngredient.recipe_id == recipe.id,
                            Ingredient.name == ingredient_name
                        ).first()

                        if recipe_ingredient:
                            current_app.logger.info(f"步骤3: 菜谱 '{recipe.name}' 含有食材 '{ingredient_name}'")

                            # 获取供应商信息
                            supplier_info = None
                            if item.batch_number:
                                # 通过批次号查找供应商
                                supplier_query = text("""
                                    SELECT s.name, s.contact_person, s.phone
                                    FROM suppliers s
                                    JOIN inventories inv ON s.id = inv.supplier_id
                                    WHERE inv.batch_number = :batch_number
                                    AND inv.ingredient_id = :ingredient_id
                                    LIMIT 1
                                """)

                                supplier_result = db.session.execute(supplier_query, {
                                    'batch_number': item.batch_number,
                                    'ingredient_id': item.ingredient_id
                                }).fetchone()

                                if supplier_result:
                                    supplier_info = {
                                        'name': supplier_result.name,
                                        'contact_person': supplier_result.contact_person,
                                        'phone': supplier_result.phone
                                    }

                            traceability_data.append({
                                'ingredient_name': ingredient_name,
                                'batch_number': item.batch_number,
                                'consumed_quantity': float(item.quantity) if item.quantity else 0,
                                'unit': item.unit,
                                'consumption_date': consumption_date,
                                'meal_type': meal_type,
                                'diners_count': consumption_plan.diners_count,
                                'recipe_name': recipe.name,
                                'recipe_category': recipe.category,
                                'planned_quantity': recipe_info['planned_quantity'],
                                'supplier_info': supplier_info,
                                'area_name': consumption_plan.menu_plan.area.name if consumption_plan.menu_plan and consumption_plan.menu_plan.area else None
                            })
                        else:
                            current_app.logger.info(f"步骤3: 菜谱 '{recipe.name}' 不含食材 '{ingredient_name}'")

        current_app.logger.info(f"溯源完成: 找到 {len(traceability_data)} 条溯源记录")
        return traceability_data

    except Exception as e:
        current_app.logger.error(f"获取出库单食材溯源信息时出错: {str(e)}")
        return []

@stock_out_bp.route('/stock-out')
@login_required
@school_required
def index(user_area):
    """出库单列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    status = request.args.get('status', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    stock_out_type = request.args.get('stock_out_type', '')

    # 构建查询
    query = StockOut.query.join(Warehouse).filter(Warehouse.area_id.in_(area_ids))

    # 应用过滤条件
    if status:
        query = query.filter(StockOut.status == status)
    if start_date:
        query = query.filter(StockOut.stock_out_date >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(StockOut.stock_out_date <= datetime.strptime(end_date + ' 23:59:59', "%Y-%m-%d %H:%M"))
    if stock_out_type:
        query = query.filter(StockOut.stock_out_type == stock_out_type)

    # 按创建时间降序排序
    query = query.order_by(StockOut.created_at.desc())

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=0)
    stock_outs = pagination.items

    return render_template('stock_out/index.html',
                          stock_outs=stock_outs,
                          pagination=pagination,
                          status=status,
                          start_date=start_date,
                          end_date=end_date,
                          stock_out_type=stock_out_type)

@stock_out_bp.route('/stock-out/<int:id>')
@login_required
def view(id):
    """查看出库单详情"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限查看该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 获取关联的消耗计划
    consumption_plan = None
    menu_plan = None
    recipes = []
    if stock_out.consumption_plan_id:
        consumption_plan = ConsumptionPlan.query.get(stock_out.consumption_plan_id)
        if consumption_plan:
            menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)

            # 通过消耗计划的日期、餐次、学校信息读取菜谱
            consumption_date = consumption_plan.consumption_date
            meal_type = consumption_plan.meal_type
            area_id = menu_plan.area_id if menu_plan else None

            current_app.logger.info(f"通过消耗计划信息读取菜谱：日期={consumption_date}, 餐次={meal_type}, 区域ID={area_id}")

            # 方法1：从菜单计划中读取菜谱
            if menu_plan:
                menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=menu_plan.id).all()
                recipes = [menu_recipe.recipe for menu_recipe in menu_recipes if menu_recipe.recipe]
                current_app.logger.info(f"从菜单计划读取到 {len(recipes)} 个食谱")

            # 方法2：如果菜单计划没有菜谱，从周菜单中读取
            if not recipes and consumption_date and meal_type and area_id:
                from app.models import WeeklyMenu, WeeklyMenuRecipe

                weekday = consumption_date.weekday()  # 0-6，0表示周一
                weekly_menu = WeeklyMenu.query.filter(
                    WeeklyMenu.week_start <= consumption_date,
                    WeeklyMenu.week_end >= consumption_date,
                    WeeklyMenu.area_id == area_id,
                    WeeklyMenu.status == '已发布'
                ).first()

                if weekly_menu:
                    weekly_recipes = WeeklyMenuRecipe.query.filter(
                        WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                        WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
                        WeeklyMenuRecipe.meal_type == meal_type
                    ).all()

                    recipes = [weekly_recipe.recipe for weekly_recipe in weekly_recipes if weekly_recipe.recipe]
                    current_app.logger.info(f"从周菜单读取到 {len(recipes)} 个食谱")

            # 记录最终结果
            current_app.logger.info(f"最终获取到 {len(recipes)} 个食谱用于关联显示")
            for recipe in recipes:
                current_app.logger.info(f"  - 食谱: {recipe.name}")

    # 获取食谱及其食材信息（排除调味品）
    recipes_with_ingredients = []
    if recipes:
        from app.models import RecipeIngredient, IngredientCategory

        # 定义调味品关键词
        seasoning_keywords = ['盐', '糖', '醋', '酱油', '料酒', '胡椒', '花椒', '八角', '桂皮', '香叶',
                             '生抽', '老抽', '蚝油', '味精', '鸡精', '十三香', '五香粉', '孜然', '辣椒粉',
                             '芝麻油', '香油', '调料', '味料', '香料', '佐料']

        for recipe in recipes:
            # 获取该食谱的所有食材
            recipe_ingredients = RecipeIngredient.query.filter_by(recipe_id=recipe.id).all()

            # 过滤掉调味品
            main_ingredients = []
            for ri in recipe_ingredients:
                if ri.ingredient:
                    ingredient_name = ri.ingredient.name
                    # 检查是否为调味品
                    is_seasoning = any(keyword in ingredient_name for keyword in seasoning_keywords)

                    # 也可以通过食材分类判断
                    if ri.ingredient.category and ri.ingredient.category.name:
                        category_name = ri.ingredient.category.name
                        if '调味品' in category_name or '香料' in category_name or '佐料' in category_name:
                            is_seasoning = True

                    if not is_seasoning:
                        main_ingredients.append(ingredient_name)

            recipes_with_ingredients.append({
                'recipe_name': recipe.name,
                'recipe_category': recipe.category,
                'main_ingredients': main_ingredients
            })

            current_app.logger.info(f"食谱 {recipe.name} 的主要食材: {main_ingredients}")

    # 获取出库明细
    stock_out_items = StockOutItem.query.filter_by(stock_out_id=id).all()

    # 获取食材溯源信息 - 显示该出库单中的食材用在哪一天哪一餐哪个食谱
    ingredient_traceability = get_stock_out_ingredient_traceability(stock_out, stock_out_items)

    # 导入溯源相关模型
    from app.models import StockInItem, StockIn, StockInDocument
    from app.models_ingredient_traceability import MaterialBatch, TraceDocument, BatchFlow

    # 获取溯源信息
    batch_traceability = {}
    for item in stock_out_items:
        batch_number = item.batch_number
        if batch_number not in batch_traceability:
            batch_info = {
                'batch_number': batch_number,
                'ingredient': item.ingredient,
                'stock_in_items': [],
                'stock_ins': [],
                'certificates': [],
                'material_batch': None,
                'trace_documents': [],
                'batch_flows': [],
                'supplier': None
            }

            try:
                # 获取入库明细
                stock_in_items = StockInItem.query.filter_by(batch_number=batch_number).all()
                batch_info['stock_in_items'] = stock_in_items

                # 获取入库单
                stock_ins = []
                for stock_in_item in stock_in_items:
                    if stock_in_item.stock_in and stock_in_item.stock_in not in stock_ins:
                        stock_ins.append(stock_in_item.stock_in)
                        # 获取供应商信息
                        if stock_in_item.stock_in.supplier and not batch_info['supplier']:
                            batch_info['supplier'] = stock_in_item.stock_in.supplier
                batch_info['stock_ins'] = stock_ins

                # 获取检验检疫证明
                certificates = []
                for stock_in in stock_ins:
                    if hasattr(stock_in, 'documents') and stock_in.documents:
                        for doc in stock_in.documents:
                            if doc not in certificates:
                                certificates.append(doc)
                batch_info['certificates'] = certificates

                # 获取溯源批次信息
                material_batch = MaterialBatch.query.filter_by(batch_number=batch_number).first()
                if material_batch:
                    batch_info['material_batch'] = material_batch

                    # 优先使用溯源批次中的供应商信息
                    if material_batch.supplier:
                        batch_info['supplier'] = material_batch.supplier

                    # 获取溯源文档
                    trace_documents = TraceDocument.query.filter_by(batch_id=material_batch.id).all()
                    batch_info['trace_documents'] = trace_documents

                    # 获取批次流水
                    batch_flows = BatchFlow.query.filter_by(batch_id=material_batch.id).all()
                    batch_info['batch_flows'] = batch_flows
            except Exception as e:
                current_app.logger.error(f"获取溯源信息失败: {str(e)}")

            batch_traceability[batch_number] = batch_info

    return render_template('stock_out/view.html',
                          stock_out=stock_out,
                          stock_out_items=stock_out_items,
                          consumption_plan=consumption_plan,
                          menu_plan=menu_plan,
                          recipes=recipes,
                          recipes_with_ingredients=recipes_with_ingredients,
                          batch_traceability=batch_traceability,
                          ingredient_traceability=ingredient_traceability)

@stock_out_bp.route('/stock-out/create', methods=['GET', 'POST'])
@login_required
@school_required
def create(user_area):
    """创建出库单"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    if request.method == 'POST':
        # 获取表单数据
        warehouse_id = request.form.get('warehouse_id', type=int)
        stock_out_date = request.form.get('stock_out_date')
        stock_out_type = request.form.get('stock_out_type')
        recipient = request.form.get('recipient')
        department = request.form.get('department')
        notes = request.form.get('notes')

        # 验证数据
        if not warehouse_id or not stock_out_date or not stock_out_type:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('stock_out.create'))

        # 检查用户是否有权限操作该仓库
        warehouse = Warehouse.query.get_or_404(warehouse_id)
        if not current_user.can_access_area_by_id(warehouse.area_id):
            flash('您没有权限操作该仓库', 'danger')
            return redirect(url_for('stock_out.index'))

        # 生成出库单号
        stock_out_number = f"CK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        try:
            # 使用原始SQL语句创建出库单，避免ORM处理datetime字段
            sql = text("""
            INSERT INTO stock_outs (
                stock_out_number, warehouse_id, stock_out_date, stock_out_type,
                recipient, department, operator_id, status, notes
            )
            OUTPUT inserted.id
            VALUES (
                :stock_out_number, :warehouse_id, :stock_out_date, :stock_out_type,
                :recipient, :department, :operator_id, :status, :notes
            )
            """).bindparams(
                stock_out_number=stock_out_number,
                warehouse_id=warehouse_id,
                stock_out_date=datetime.strptime(stock_out_date, '%Y-%m-%d'),
                stock_out_type=stock_out_type,
                recipient=recipient,
                department=department,
                operator_id=current_user.id,
                status='待审核',
                notes=notes
            )

            # 执行SQL语句
            result = db.session.execute(sql)

            # 获取新创建的出库单ID
            stock_out_id = result.fetchone()[0]

            # 提交事务
            db.session.commit()

            flash('出库单创建成功，请添加出库明细', 'success')
            return redirect(url_for('stock_out.edit', id=stock_out_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建出库单时出错: {str(e)}")
            flash(f'创建出库单时出错: {str(e)}', 'danger')
            return redirect(url_for('stock_out.create'))

    # GET请求，显示创建表单
    # 获取仓库列表
    warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids), Warehouse.status == '正常').all()

    return render_template('stock_out/form.html',
                          stock_out=None,
                          warehouses=warehouses,
                          title='创建出库单')

@stock_out_bp.route('/stock-out/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑出库单"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限编辑该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核状态的出库单可以编辑
    if stock_out.status != '待审核':
        flash('只有待审核状态的出库单可以编辑', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    if request.method == 'POST':
        # 获取表单数据
        stock_out_date = request.form.get('stock_out_date')
        stock_out_type = request.form.get('stock_out_type')
        recipient = request.form.get('recipient')
        department = request.form.get('department')
        notes = request.form.get('notes')

        # 验证数据
        if not stock_out_date or not stock_out_type:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('stock_out.edit', id=id))

        try:
            # 使用原始SQL语句更新出库单信息，避免ORM处理datetime字段
            sql = text("""
            UPDATE stock_outs
            SET stock_out_date = :stock_out_date,
                stock_out_type = :stock_out_type,
                recipient = :recipient,
                department = :department,
                notes = :notes
            WHERE id = :id
            """).bindparams(
                stock_out_date=datetime.strptime(stock_out_date, '%Y-%m-%d'),
                stock_out_type=stock_out_type,
                recipient=recipient,
                department=department,
                notes=notes,
                id=id
            )

            # 执行SQL语句
            db.session.execute(sql)

            # 提交事务
            db.session.commit()

            flash('出库单信息更新成功', 'success')
            return redirect(url_for('stock_out.view', id=id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新出库单时出错: {str(e)}")
            flash(f'更新出库单时出错: {str(e)}', 'danger')
            return redirect(url_for('stock_out.edit', id=id))

    # GET请求，显示编辑表单
    # 获取出库明细
    stock_out_items = StockOutItem.query.filter_by(stock_out_id=id).all()

    # 获取食材列表
    ingredients = Ingredient.query.all()

    # 获取库存列表（按食材分组）
    inventory_by_ingredient = {}
    for ingredient in ingredients:
        # 查询库存，确保只返回状态为"正常"且数量大于0的记录
        inventory_items = Inventory.query.filter(
            Inventory.warehouse_id == stock_out.warehouse_id,
            Inventory.ingredient_id == ingredient.id,
            Inventory.status == '正常',
            Inventory.quantity > 0
        ).order_by(Inventory.expiry_date).all()

        # 记录查询结果
        if inventory_items:
            current_app.logger.info(f"食材 {ingredient.name} 找到 {len(inventory_items)} 条库存记录")

        if inventory_items:
            inventory_by_ingredient[ingredient.id] = inventory_items

    return render_template('stock_out/edit.html',
                          stock_out=stock_out,
                          stock_out_items=stock_out_items,
                          ingredients=ingredients,
                          inventory_by_ingredient=inventory_by_ingredient)

@stock_out_bp.route('/stock-out/<int:id>/add-item', methods=['POST'])
@login_required
def add_item(id):
    """添加出库明细"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限编辑该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核状态的出库单可以添加明细
    if stock_out.status != '待审核':
        flash('只有待审核状态的出库单可以添加明细', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    # 获取表单数据
    inventory_id = request.form.get('inventory_id', type=int)
    quantity = request.form.get('quantity', type=float)
    notes = request.form.get('notes')

    # 验证数据
    if not inventory_id or not quantity or quantity <= 0:
        flash('请填写所有必填字段，且数量必须大于0', 'danger')
        return redirect(url_for('stock_out.edit', id=id))

    # 获取库存信息
    inventory = Inventory.query.get_or_404(inventory_id)

    # 检查库存是否属于该仓库
    if inventory.warehouse_id != stock_out.warehouse_id:
        flash('所选库存不属于该出库单的仓库', 'danger')
        return redirect(url_for('stock_out.edit', id=id))

    # 检查库存是否足够
    if inventory.quantity < quantity:
        flash(f'库存不足，当前库存: {inventory.quantity} {inventory.unit}', 'danger')
        return redirect(url_for('stock_out.edit', id=id))

    # 创建出库明细
    stock_out_item = StockOutItem(
        stock_out_id=id,
        inventory_id=inventory_id,
        ingredient_id=inventory.ingredient_id,
        batch_number=inventory.batch_number,
        quantity=quantity,
        unit=inventory.unit,
        notes=notes
    )

    db.session.add(stock_out_item)
    db.session.commit()

    flash('出库明细添加成功', 'success')
    return redirect(url_for('stock_out.edit', id=id))

@stock_out_bp.route('/stock-out/<int:id>/remove-item/<int:item_id>', methods=['POST'])
@login_required
def remove_item(id, item_id):
    """删除出库明细"""
    stock_out = StockOut.query.get_or_404(id)
    stock_out_item = StockOutItem.query.get_or_404(item_id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限编辑该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核状态的出库单可以删除明细
    if stock_out.status != '待审核':
        flash('只有待审核状态的出库单可以删除明细', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    # 检查明细是否属于该出库单
    if stock_out_item.stock_out_id != id:
        flash('该明细不属于当前出库单', 'danger')
        return redirect(url_for('stock_out.edit', id=id))

    # 删除明细
    db.session.delete(stock_out_item)
    db.session.commit()

    flash('出库明细删除成功', 'success')
    return redirect(url_for('stock_out.edit', id=id))

@stock_out_bp.route('/stock-out/<int:id>/approve', methods=['POST'])
@login_required
def approve(id):
    """审核出库单"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限审核
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限审核该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核状态的出库单可以审核
    if stock_out.status != '待审核':
        flash('只有待审核状态的出库单可以审核', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    # 检查是否有出库明细
    if StockOutItem.query.filter_by(stock_out_id=id).count() == 0:
        flash('出库单没有明细，无法审核', 'warning')
        return redirect(url_for('stock_out.edit', id=id))

    try:
        # 使用原始SQL语句更新出库单状态，避免ORM处理datetime字段
        sql = text("""
        UPDATE stock_outs
        SET status = '已审核',
            approver_id = :approver_id
        WHERE id = :id
        """).bindparams(
            approver_id=current_user.id,
            id=id
        )

        # 执行SQL语句
        db.session.execute(sql)

        # 提交事务
        db.session.commit()

        flash('出库单审核成功', 'success')
        return redirect(url_for('stock_out.view', id=id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"审核出库单时出错: {str(e)}")
        flash(f'审核出库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_out.view', id=id))

@stock_out_bp.route('/stock-out/<int:id>/execute', methods=['POST'])
@login_required
def execute(id):
    """执行出库单（更新库存）"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限执行
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限执行该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有已审核状态的出库单可以执行
    if stock_out.status != '已审核':
        flash('只有已审核状态的出库单可以执行', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    # 获取出库明细
    stock_out_items = StockOutItem.query.filter_by(stock_out_id=id).all()

    # 更新库存
    for item in stock_out_items:
        inventory = Inventory.query.get(item.inventory_id)
        if inventory:
            # 检查库存是否足够
            if inventory.quantity < item.quantity:
                flash(f'食材 {item.ingredient.name} 库存不足，当前库存: {inventory.quantity} {inventory.unit}', 'danger')
                return redirect(url_for('stock_out.view', id=id))

            # 更新库存
            old_quantity = inventory.quantity
            inventory.quantity -= item.quantity

            # 记录库存更新
            current_app.logger.info(f"更新库存: 食材={item.ingredient.name}, 批次号={inventory.batch_number}, 原数量={old_quantity}, 减少={item.quantity}, 新数量={inventory.quantity}")

            # 如果库存数量小于等于0，将状态更新为"已用完"
            if inventory.quantity <= 0:
                inventory.status = '已用完'
                current_app.logger.info(f"库存已用完: 食材={item.ingredient.name}, 批次号={inventory.batch_number}, 状态更新为'已用完'")

    try:
        # 使用原始SQL语句更新出库单状态，避免ORM处理datetime字段
        sql = text("""
        UPDATE stock_outs
        SET status = '已出库'
        WHERE id = :id
        """).bindparams(
            id=id
        )

        # 执行SQL语句
        db.session.execute(sql)

        # 提交事务
        db.session.commit()

        flash('出库单执行成功，库存已更新', 'success')
        return redirect(url_for('stock_out.view', id=id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"执行出库单时出错: {str(e)}")
        flash(f'执行出库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_out.view', id=id))

@stock_out_bp.route('/stock-out/<int:id>/cancel', methods=['POST'])
@login_required
def cancel(id):
    """取消出库单"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限取消
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限取消该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 只有待审核或已审核状态的出库单可以取消
    if stock_out.status not in ['待审核', '已审核']:
        flash('只有待审核或已审核状态的出库单可以取消', 'warning')
        return redirect(url_for('stock_out.view', id=id))

    try:
        # 使用原始SQL语句更新出库单状态，避免ORM处理datetime字段
        sql = text("""
        UPDATE stock_outs
        SET status = '已取消'
        WHERE id = :id
        """).bindparams(
            id=id
        )

        # 执行SQL语句
        db.session.execute(sql)

        # 提交事务
        db.session.commit()

        flash('出库单已取消', 'success')
        return redirect(url_for('stock_out.view', id=id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"取消出库单时出错: {str(e)}")
        flash(f'取消出库单时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_out.view', id=id))

@stock_out_bp.route('/stock-out/<int:id>/print')
@login_required
def print_stock_out(id):
    """打印出库单"""
    stock_out = StockOut.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(stock_out.warehouse.area_id):
        flash('您没有权限查看该出库单', 'danger')
        return redirect(url_for('stock_out.index'))

    # 获取关联的消耗计划
    consumption_plan = None
    menu_plan = None
    if stock_out.consumption_plan_id:
        consumption_plan = ConsumptionPlan.query.get(stock_out.consumption_plan_id)
        if consumption_plan:
            menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)

    # 获取出库明细
    stock_out_items = StockOutItem.query.filter_by(stock_out_id=id).all()

    return render_template('stock_out/print.html',
                          stock_out=stock_out,
                          stock_out_items=stock_out_items,
                          consumption_plan=consumption_plan,
                          menu_plan=menu_plan)
