{% extends 'base.html' %}

{% block title %}食材溯源{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材溯源</h3>
                </div>
                <div class="card-body">
                    <!-- 查询表单 -->
                    <form method="get" action="{{ url_for('food_trace.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>区域</label>
                                    <select name="area_id" class="form-control">
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>{{ area.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>日期</label>
                                    <input type="date" name="date" class="form-control" value="{{ trace_date }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>餐次</label>
                                    <select name="meal_type" class="form-control">
                                        <option value="早餐" {% if meal_type == '早餐' %}selected{% endif %}>早餐</option>
                                        <option value="午餐" {% if meal_type == '午餐' %}selected{% endif %}>午餐</option>
                                        <option value="晚餐" {% if meal_type == '晚餐' %}selected{% endif %}>晚餐</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">查询</button>
                                        <a href="{{ url_for('food_trace.print_samples', area_id=area_id, date=trace_date, meal_type=meal_type) }}" class="btn btn-secondary" target="_blank">
                                            <i class="fas fa-print"></i> 打印留样记录
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    {% if error_message %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> {{ error_message }}
                    </div>
                    {% endif %}

                    {% if trace_data %}
                    <!-- 溯源概览 -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">溯源概览</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <p><strong>日期：</strong> {{ trace_date }}</p>
                                </div>
                                <div class="col-md-3">
                                    <p><strong>餐次：</strong> {{ meal_type }}</p>
                                </div>
                                <div class="col-md-3">
                                    {% if trace_data.consumption_plan %}
                                    <p><strong>用餐人数：</strong> {{ trace_data.consumption_plan.diners_count|default('-') }}</p>
                                    {% endif %}
                                </div>
                                <div class="col-md-3">
                                    {% if trace_data.stock_out %}
                                    <p><strong>出库单号：</strong> {{ trace_data.stock_out.stock_out_number }}</p>
                                    {% endif %}
                                </div>
                            </div>

                            {% if trace_data.recipes %}
                            <h6 class="mt-3">菜品列表</h6>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>菜品名称</th>
                                            <th>分类</th>
                                            <th>主要食材</th>
                                            <th>来源</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for recipe in trace_data.recipes %}
                                        <tr>
                                            <td>{{ recipe.name }}</td>
                                            <td>{{ recipe.category|default('-') }}</td>
                                            <td>
                                                {% if recipe.main_ingredients %}
                                                    {% for ingredient in recipe.main_ingredients %}
                                                        <span class="badge badge-secondary">{{ ingredient }}</span>
                                                    {% endfor %}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if recipe.source == 'weekly_menu' %}
                                                    <span class="badge badge-success">周菜单</span>
                                                {% elif recipe.source == 'menu_plan' %}
                                                    <span class="badge badge-info">日菜单</span>
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-info view-recipe" data-id="{{ recipe.id }}">
                                                    查看详情
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    {% if trace_data.batches %}
                    <!-- 批次溯源信息 -->
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">批次溯源信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材名称</th>
                                            <th>批次号</th>
                                            <th>出库数量</th>
                                            <th>过期日期</th>
                                            <th>供应商</th>
                                            <th>联系方式</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for batch in trace_data.batches %}
                                        <tr>
                                            <td>{{ batch.ingredient_name|default('-') }}</td>
                                            <td>
                                                <span class="badge badge-primary">{{ batch.batch_number }}</span>
                                            </td>
                                            <td>{{ batch.quantity }} {{ batch.unit }}</td>
                                            <td>
                                                {% if batch.expiry_date %}
                                                    {{ batch.expiry_date }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if batch.supplier %}
                                                    {{ batch.supplier.name }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if batch.supplier %}
                                                    {% if batch.supplier.contact_person %}
                                                        {{ batch.supplier.contact_person }}<br>
                                                    {% endif %}
                                                    {% if batch.supplier.phone %}
                                                        <small>{{ batch.supplier.phone }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 溯源链 -->
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">完整溯源链</h5>
                        </div>
                        <div class="card-body">
                            <div class="trace-chain">
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-utensils"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6>菜单计划</h6>
                                        <p>{{ trace_date }} {{ meal_type }}</p>
                                        <small>{{ trace_data.recipes|length }} 个菜品</small>
                                    </div>
                                </div>
                                <div class="trace-arrow">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6>消耗计划</h6>
                                        {% if trace_data.consumption_plan %}
                                        <p>计划ID: {{ trace_data.consumption_plan.id }}</p>
                                        <small>用餐人数: {{ trace_data.consumption_plan.diners_count|default('-') }}</small>
                                        {% else %}
                                        <p>暂无消耗计划</p>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="trace-arrow">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-dolly-flatbed"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6>出库记录</h6>
                                        {% if trace_data.stock_out %}
                                        <p>{{ trace_data.stock_out.stock_out_number }}</p>
                                        <small>{{ trace_data.stock_out.stock_out_date }} - {{ trace_data.stock_out.status }}</small>
                                        {% else %}
                                        <p>暂无出库记录</p>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="trace-arrow">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-boxes"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6>批次信息</h6>
                                        <p>{{ trace_data.batches|length }} 个批次</p>
                                        {% if trace_data.batches %}
                                        {% set ingredient_names = [] %}
                                        {% for batch in trace_data.batches %}
                                            {% if batch.ingredient_name and batch.ingredient_name not in ingredient_names %}
                                                {% set _ = ingredient_names.append(batch.ingredient_name) %}
                                            {% endif %}
                                        {% endfor %}
                                        <small>涉及 {{ ingredient_names|length }} 种食材</small>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="trace-arrow">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="trace-step">
                                    <div class="trace-icon">
                                        <i class="fas fa-industry"></i>
                                    </div>
                                    <div class="trace-content">
                                        <h6>供应商</h6>
                                        {% set supplier_names = [] %}
                                        {% for batch in trace_data.batches %}
                                            {% if batch.supplier and batch.supplier.name not in supplier_names %}
                                                {% set _ = supplier_names.append(batch.supplier.name) %}
                                            {% endif %}
                                        {% endfor %}
                                        <p>{{ supplier_names|length }} 个供应商</p>
                                        {% if supplier_names %}
                                        <small>
                                            {% for name in supplier_names[:3] %}
                                                {{ name }}{% if not loop.last %}, {% endif %}
                                            {% endfor %}
                                            {% if supplier_names|length > 3 %}等{% endif %}
                                        </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 留样记录 -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">留样记录</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>留样编号</th>
                                            <th>食谱名称</th>
                                            <th>留样图片</th>
                                            <th>留样数量</th>
                                            <th>留样时间</th>
                                            <th>销毁时间</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for sample in trace_data.food_samples %}
                                        <tr>
                                            <td>{{ sample.sample_number }}</td>
                                            <td>{{ sample.recipe_name }}</td>
                                            <td>
                                                {% if sample.sample_image %}
                                                <a href="{{ url_for('static', filename=sample.sample_image) }}" target="_blank">
                                                    <img src="{{ url_for('static', filename=sample.sample_image) }}" alt="留样图片" style="max-height: 50px;">
                                                </a>
                                                {% else %}
                                                无图片
                                                {% endif %}
                                            </td>
                                            <td>{{ sample.sample_quantity }} {{ sample.sample_unit }}</td>
                                            <td>{{ sample.start_time }}</td>
                                            <td>{{ sample.end_time }}</td>
                                            <td>{{ sample.status }}</td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">暂无留样记录</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        请选择区域、日期和餐次进行查询
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 食谱详情模态框 -->
<div class="modal fade" id="recipeModal" tabindex="-1" role="dialog" aria-labelledby="recipeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recipeModalLabel">食谱详情</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="recipeDetails">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .trace-chain {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .trace-step {
        display: flex;
        align-items: center;
        width: 100%;
        max-width: 500px;
        margin-bottom: 10px;
        padding: 15px;
        border-radius: 5px;
        background-color: #f8f9fa;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .trace-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #007bff;
        color: white;
        border-radius: 50%;
        margin-right: 15px;
        font-size: 20px;
    }

    .trace-content {
        flex: 1;
    }

    .trace-content h6 {
        margin-bottom: 5px;
        font-weight: bold;
    }

    .trace-content p {
        margin-bottom: 0;
        color: #6c757d;
    }

    .trace-arrow {
        margin: 5px 0;
        color: #6c757d;
        font-size: 20px;
    }
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    $(document).ready(function() {
        // 查看食谱详情
        $('.view-recipe').click(function() {
            var recipeId = $(this).data('id');
            $('#recipeModal').modal('show');

            // 加载食谱详情
            $.ajax({
                url: '/food-trace-api/recipe/' + recipeId,
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        var recipe = response.data;
                        var html = '<div class="row">';

                        // 食谱基本信息
                        html += '<div class="col-md-6">';
                        html += '<h5>' + recipe.name + '</h5>';
                        html += '<p><strong>分类：</strong> ' + recipe.category + '</p>';
                        html += '<p><strong>描述：</strong> ' + (recipe.description || '无') + '</p>';
                        html += '</div>';

                        // 食谱图片
                        html += '<div class="col-md-6 text-center">';
                        if (recipe.image) {
                            html += '<img src="' + recipe.image + '" alt="' + recipe.name + '" class="img-fluid" style="max-height: 200px;">';
                        } else {
                            html += '<p>无图片</p>';
                        }
                        html += '</div>';

                        html += '</div>';

                        // 食谱材料
                        html += '<h6 class="mt-3">食材列表</h6>';
                        html += '<div class="table-responsive">';
                        html += '<table class="table table-bordered table-sm">';
                        html += '<thead><tr><th>食材名称</th><th>数量</th><th>单位</th></tr></thead>';
                        html += '<tbody>';

                        if (recipe.ingredients && recipe.ingredients.length > 0) {
                            recipe.ingredients.forEach(function(ingredient) {
                                html += '<tr>';
                                html += '<td>' + ingredient.name + '</td>';
                                html += '<td>' + ingredient.quantity + '</td>';
                                html += '<td>' + ingredient.unit + '</td>';
                                html += '</tr>';
                            });
                        } else {
                            html += '<tr><td colspan="3" class="text-center">暂无食材数据</td></tr>';
                        }

                        html += '</tbody></table></div>';

                        $('#recipeDetails').html(html);
                    } else {
                        $('#recipeDetails').html('<div class="alert alert-danger">' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#recipeDetails').html('<div class="alert alert-danger">加载食谱详情失败</div>');
                }
            });
        });
    });
</script>
{% endblock %}
