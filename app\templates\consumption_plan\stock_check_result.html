{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-exclamation-triangle text-warning"></i> 库存不足警告
                    </h3>
                    <div>
                        <a href="{{ url_for('consumption_plan.create_from_weekly', weekly_menu_id=weekly_menu_id) }}" 
                           class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回选择
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 警告信息 -->
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> 库存不足提醒</h5>
                        <p class="mb-2">
                            <strong>{{ warehouse.name }}</strong> 仓库中有 <strong>{{ insufficient_ingredients|length }}</strong> 种食材库存不足，
                            无法满足所选餐次的消耗需求。
                        </p>
                        <p class="mb-0">
                            建议您先创建采购计划补充库存，或者调整消耗计划的餐次选择。
                        </p>
                    </div>
                    
                    <!-- 库存不足明细 -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-warning">
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="25%">食材名称</th>
                                    <th width="15%">需要数量</th>
                                    <th width="15%">当前库存</th>
                                    <th width="15%">缺口数量</th>
                                    <th width="10%">单位</th>
                                    <th width="15%">缺口比例</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in insufficient_ingredients %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <strong>{{ item.name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ item.needed }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{ item.current }}</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-danger">{{ item.shortage }}</span>
                                    </td>
                                    <td>{{ item.unit }}</td>
                                    <td>
                                        {% set shortage_percent = (item.shortage / item.needed * 100) | round(1) %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-danger" 
                                                 style="width: {{ shortage_percent }}%">
                                                {{ shortage_percent }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 操作选项 -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-shopping-cart"></i> 推荐方案：创建采购计划
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        系统可以根据库存缺口自动创建采购计划，补充所需食材后再创建消耗计划。
                                    </p>
                                    <form method="post" action="{{ url_for('consumption_plan.create_purchase_plan_from_shortage') }}">
                                        <input type="hidden" name="weekly_menu_id" value="{{ weekly_menu_id }}">
                                        <input type="hidden" name="warehouse_id" value="{{ warehouse_id }}">
                                        <input type="hidden" name="selected_meals" value="{{ selected_meals | join(',') }}">
                                        {% for item in insufficient_ingredients %}
                                        <input type="hidden" name="shortage_{{ item.ingredient_id }}" value="{{ item.shortage }}">
                                        {% endfor %}
                                        
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-plus"></i> 创建采购计划
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="mb-0">
                                        <i class="fas fa-exclamation-circle"></i> 强制创建消耗计划
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        忽略库存不足警告，强制创建消耗计划。<br>
                                        <small class="text-muted">注意：这可能导致后续执行时库存不足。</small>
                                    </p>
                                    <form method="post" action="{{ url_for('consumption_plan.create_from_weekly_force') }}">
                                        <input type="hidden" name="weekly_menu_id" value="{{ weekly_menu_id }}">
                                        <input type="hidden" name="warehouse_id" value="{{ warehouse_id }}">
                                        <input type="hidden" name="selected_meals" value="{{ selected_meals | join(',') }}">
                                        
                                        <button type="submit" class="btn btn-warning" 
                                                onclick="return confirm('确定要忽略库存不足警告，强制创建消耗计划吗？')">
                                            <i class="fas fa-exclamation-triangle"></i> 强制创建
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 选择的餐次信息 -->
                    <div class="mt-4">
                        <h6>所选餐次：</h6>
                        <div class="d-flex flex-wrap">
                            {% for meal in selected_meals %}
                            {% set date_str, meal_type = meal.split('_') %}
                            <span class="badge badge-primary mr-2 mb-2">
                                {{ date_str }} {{ meal_type }}
                            </span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
.table th {
    background-color: #fff3cd;
    color: #856404;
    font-weight: 600;
    border-bottom: 2px solid #ffeaa7;
}

.progress {
    background-color: #e9ecef;
}

.card-header h5 {
    margin-bottom: 0;
}

.badge {
    font-size: 0.9em;
}

.alert-warning {
    border-left: 4px solid #ffc107;
}
</style>
{% endblock %}
