from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, current_app, abort
from flask_login import login_required, current_user
from app.models import (
    ConsumptionPlan, ConsumptionDetail, MenuPlan, MenuRecipe, Recipe,
    RecipeIngredient, Ingredient, Inventory, InventoryAlert, AdministrativeArea,
    Warehouse, StockOut, StockOutItem, IngredientCategory, User, Role
)
from app import db
from datetime import datetime, date, timedelta
import json
import uuid
from sqlalchemy import text
from app.utils.balance_stock_in import create_balance_stock_in

consumption_plan_bp = Blueprint('consumption_plan', __name__)

@consumption_plan_bp.route('/consumption-plan')
@login_required
def index():
    """消耗计划列表页面"""
    try:
        # 简单直接：使用当前用户的学校进行筛选
        user_area_id = current_user.area_id

        # 调试信息：显示用户权限信息
        current_app.logger.info(f"当前用户: {current_user.username}")
        current_app.logger.info(f"用户区域ID: {user_area_id}")
        current_app.logger.info(f"用户区域名称: {current_user.area.name if current_user.area else '无'}")
        current_app.logger.info(f"是否管理员: {current_user.is_admin()}")

        # 如果用户没有区域，显示警告
        if not user_area_id:
            flash("您的账号没有关联任何学校区域，请联系管理员设置", "warning")

        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = current_app.config['ITEMS_PER_PAGE']
        status = request.args.get('status', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        # 使用SQLAlchemy ORM查询，按学校筛选
        query = db.session.query(
            ConsumptionPlan, MenuPlan
        ).outerjoin(
            MenuPlan, ConsumptionPlan.menu_plan_id == MenuPlan.id
        )

        # 按学校筛选：优先使用消耗计划的area_id，其次使用菜单计划的area_id
        if user_area_id:
            query = query.filter(
                db.or_(
                    ConsumptionPlan.area_id == user_area_id,  # 直接从消耗计划获取学校信息
                    db.and_(ConsumptionPlan.area_id.is_(None), MenuPlan.area_id == user_area_id)  # 从菜单计划获取学校信息
                )
            )
        else:
            # 如果用户没有区域，返回空结果
            query = query.filter(ConsumptionPlan.id == -1)

        # 应用状态过滤
        if status:
            query = query.filter(ConsumptionPlan.status == status)

        # 应用日期过滤
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(ConsumptionPlan.consumption_date >= start_date_obj)

        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(ConsumptionPlan.consumption_date <= end_date_obj)

        # 按创建时间降序排序
        query = query.order_by(ConsumptionPlan.created_at.desc())

        # 执行查询
        results = query.all()

        # 收集所有需要的用户ID和区域ID
        user_ids = set()
        area_ids_for_lookup = set()

        for cp, mp in results:
            if cp.created_by:
                user_ids.add(cp.created_by)
            if cp.approved_by:
                user_ids.add(cp.approved_by)
            # 优先使用消耗计划的area_id，其次使用菜单计划的area_id
            if cp.area_id:
                area_ids_for_lookup.add(cp.area_id)
            elif mp and mp.area_id:
                area_ids_for_lookup.add(mp.area_id)

        # 预加载所有需要的用户和区域信息
        users = {}
        if user_ids:
            user_objects = User.query.filter(User.id.in_(user_ids)).all()
            for user in user_objects:
                users[user.id] = user

        areas = {}
        if area_ids_for_lookup:
            area_objects = AdministrativeArea.query.filter(AdministrativeArea.id.in_(area_ids_for_lookup)).all()
            for area in area_objects:
                areas[area.id] = area

        # 处理结果
        consumption_plans = []
        for cp, mp in results:
            # 创建一个字典来存储消耗计划数据
            plan = {
                'id': cp.id,
                'menu_plan_id': cp.menu_plan_id,
                'status': cp.status,
                'created_by': cp.created_by,
                'approved_by': cp.approved_by,
                'notes': cp.notes,
                'created_at': cp.created_at,
                'updated_at': cp.updated_at,
                'consumption_date': cp.consumption_date,
                'meal_type': cp.meal_type,
                'diners_count': cp.diners_count,
                'menu_plan': None,
                'creator': {'real_name': '', 'username': ''},
                'approver': {'real_name': '', 'username': ''}
            }

            # 设置区域信息（优先使用消耗计划的area_id）
            area_id = cp.area_id or (mp.area_id if mp else None)
            if area_id and area_id in areas:
                plan['area_name'] = areas[area_id].name
                plan['area_id'] = area_id

            # 如果有关联的菜单计划，添加菜单计划信息
            if mp:
                plan['menu_plan'] = {
                    'plan_date': mp.plan_date,
                    'meal_type': mp.meal_type,
                    'expected_diners': mp.expected_diners,
                    'area_id': mp.area_id,
                    'area': {'name': areas.get(mp.area_id, {}).get('name', '') if mp.area_id else ''}
                }

            # 设置创建者信息
            if cp.created_by and cp.created_by in users:
                plan['creator']['real_name'] = users[cp.created_by].real_name
                plan['creator']['username'] = users[cp.created_by].username

            # 设置审核者信息
            if cp.approved_by and cp.approved_by in users:
                plan['approver']['real_name'] = users[cp.approved_by].real_name
                plan['approver']['username'] = users[cp.approved_by].username

            consumption_plans.append(plan)



        # 简单分页处理
        total = len(consumption_plans)
        start = (page - 1) * per_page
        end = start + per_page
        paginated_plans = consumption_plans[start:end]

        # 创建一个简单的分页对象
        class SimplePagination:
            def __init__(self, items, page, per_page, total):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total
                self.pages = (total + per_page - 1) // per_page
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1
                self.next_num = page + 1

            def iter_pages(self):
                for i in range(1, self.pages + 1):
                    if i <= 2 or i >= self.pages - 1 or abs(i - self.page) <= 2:
                        yield i
                    elif i == self.page - 3 or i == self.page + 3:
                        yield None

        pagination = SimplePagination(paginated_plans, page, per_page, total)

        # 获取各状态的计划数量
        plan_counts = {
            'planning': 0,
            'approved': 0,
            'executed': 0,
            'cancelled': 0
        }

        # 计算各状态的计划数量
        for plan in consumption_plans:
            if plan['status'] == '计划中':
                plan_counts['planning'] += 1
            elif plan['status'] == '已审核':
                plan_counts['approved'] += 1
            elif plan['status'] == '已执行':
                plan_counts['executed'] += 1
            elif plan['status'] == '已取消':
                plan_counts['cancelled'] += 1

        return render_template('consumption_plan/index.html',
                              consumption_plans=paginated_plans,
                              pagination=pagination,
                              status=status,
                              start_date=start_date,
                              end_date=end_date,
                              plan_counts=plan_counts)

    except Exception as e:
        # 记录错误并返回友好的错误信息
        current_app.logger.error(f"获取消耗计划列表失败: {str(e)}")
        flash(f'获取消耗计划列表失败: {str(e)}', 'danger')
        return render_template('consumption_plan/index.html',
                              consumption_plans=[],
                              pagination=None,
                              status=status,
                              start_date=start_date,
                              end_date=end_date,
                              plan_counts={'planning': 0, 'approved': 0, 'executed': 0, 'cancelled': 0})

@consumption_plan_bp.route('/consumption-plan/create/<int:menu_plan_id>', methods=['GET', 'POST'])
@login_required
def create(menu_plan_id):
    """创建消耗计划"""
    from datetime import date

    menu_plan = MenuPlan.query.get_or_404(menu_plan_id)

    # 检查用户是否有权限操作该区域
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限操作该区域', 'danger')
        return redirect(url_for('consumption_plan.index'))

    # 检查菜单计划状态
    if menu_plan.status != '已发布':
        flash('只有已发布状态的菜单计划可以创建消耗计划', 'danger')
        return redirect(url_for('menu_plan.view', id=menu_plan_id))

    # 检查是否已经存在消耗计划
    existing_plan = ConsumptionPlan.query.filter_by(menu_plan_id=menu_plan_id).first()
    if existing_plan:
        flash('该菜单计划已经创建了消耗计划', 'warning')
        return redirect(url_for('consumption_plan.view', id=existing_plan.id))

    if request.method == 'POST':
        # 获取表单数据
        consumption_date = request.form.get('consumption_date')
        meal_type = request.form.get('meal_type')
        diners_count = request.form.get('diners_count', type=int)
        notes = request.form.get('notes')

        # 获取选中的食材
        selected_ingredients = request.form.getlist('selected_ingredients')

        # 获取调味品数据
        condiment_names = request.form.getlist('condiment_name[]')
        condiment_quantities = request.form.getlist('condiment_quantity[]')
        condiment_units = request.form.getlist('condiment_unit[]')

        # 创建消耗计划
        consumption_plan = ConsumptionPlan(
            menu_plan_id=menu_plan_id,
            status='计划中',
            created_by=current_user.id,
            notes=notes,
            consumption_date=datetime.strptime(consumption_date, '%Y-%m-%d').date() if consumption_date else menu_plan.plan_date,
            meal_type=meal_type or menu_plan.meal_type,
            diners_count=diners_count or menu_plan.expected_diners
        )

        db.session.add(consumption_plan)
        db.session.flush()  # 获取ID

        # 添加主要食材消耗明细
        for ingredient_id in selected_ingredients:
            quantity = request.form.get(f'quantity_{ingredient_id}', type=float)
            if quantity and quantity > 0:
                # 获取食材信息
                ingredient = Ingredient.query.get(ingredient_id)

                # 获取库存中的单位
                inventory = Inventory.query.filter_by(
                    ingredient_id=ingredient_id,
                    warehouse_id=Warehouse.query.filter_by(area_id=menu_plan.area_id).first().id
                ).first()

                unit = inventory.unit if inventory else ingredient.standard_unit

                # 创建消耗明细，将主要食材信息存储在 notes 字段
                notes = "主要食材"
                if inventory:
                    notes += f"，库存ID: {inventory.id}"

                # 创建消耗明细
                consumption_detail = ConsumptionDetail(
                    consumption_plan_id=consumption_plan.id,
                    ingredient_id=ingredient_id,
                    planned_quantity=quantity,
                    unit=unit,
                    status='待出库',
                    notes=notes
                )
                db.session.add(consumption_detail)

        # 添加调味品消耗明细
        for i in range(len(condiment_names)):
            name = condiment_names[i].strip()
            quantity = float(condiment_quantities[i]) if condiment_quantities[i] else 0
            unit = condiment_units[i].strip()

            if name and quantity > 0 and unit:
                # 查找是否存在该调味品
                ingredient = Ingredient.query.filter_by(name=name).first()

                if not ingredient:
                    # 创建新的调味品食材
                    ingredient = Ingredient(
                        name=name,
                        category_id=Ingredient.query.filter_by(name='调味品').first().category_id,
                        standard_unit=unit,
                        is_condiment=True
                    )
                    db.session.add(ingredient)
                    db.session.flush()  # 获取ID

                # 创建消耗明细
                consumption_detail = ConsumptionDetail(
                    consumption_plan_id=consumption_plan.id,
                    ingredient_id=ingredient.id,
                    planned_quantity=quantity,
                    unit=unit,
                    status='待出库',
                    is_main_ingredient=False
                )
                db.session.add(consumption_detail)

        db.session.commit()

        # 检查库存是否足够
        check_inventory(consumption_plan.id, menu_plan.area_id)

        flash('消耗计划创建成功', 'success')
        return redirect(url_for('consumption_plan.view', id=consumption_plan.id))

    # GET请求，显示创建表单
    # 获取菜单中的所有食谱
    menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=menu_plan_id).all()

    # 获取食材分类
    ingredient_categories = db.session.query(Ingredient.category).distinct().all()

    # 获取该区域的仓库
    warehouse = Warehouse.query.filter_by(area_id=menu_plan.area_id).first()

    # 按分类获取库存食材
    category_inventories = {}
    if warehouse:
        # 获取所有食材分类
        from sqlalchemy import func
        categories = db.session.query(Ingredient.category_id).distinct().all()

        for category_id in [c[0] for c in categories]:
            # 获取该分类下的库存食材
            inventories = db.session.query(
                Inventory.ingredient_id,
                Inventory.unit,
                func.sum(Inventory.quantity).label('total_quantity'),
                Ingredient
            ).join(
                Ingredient, Inventory.ingredient_id == Ingredient.id
            ).filter(
                Inventory.warehouse_id == warehouse.id,
                Inventory.status == '正常',
                Ingredient.category_id == category_id,
                Inventory.quantity > 0,
                Ingredient.is_condiment == False  # 排除调味品
            ).group_by(
                Inventory.ingredient_id,
                Inventory.unit,
                Ingredient.id
            ).all()

            category_inventories[category_id] = inventories

    return render_template('consumption_plan/create.html',
                          menu_plan=menu_plan,
                          menu_recipes=menu_recipes,
                          ingredient_categories=ingredient_categories,
                          category_inventories=category_inventories,
                          today_date=date.today().strftime('%Y-%m-%d'))

@consumption_plan_bp.route('/consumption-plan/<int:id>')
@login_required
def view(id):
    """查看消耗计划详情"""
    try:
        # 使用一次查询获取消耗计划及其关联的菜单计划
        consumption_plan = ConsumptionPlan.query.get_or_404(id)

        # 获取区域ID和区域名称 - 优先使用消耗计划的 area_id 字段
        area_id = consumption_plan.area_id
        area_name = None
        menu_plan = None

        if area_id:
            # 获取区域名称
            area = AdministrativeArea.query.get(area_id)
            if area:
                area_name = area.name

        if consumption_plan.menu_plan_id:
            # 获取菜单计划信息
            menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)
            if menu_plan and not area_id:
                # 如果消耗计划没有 area_id，从菜单计划获取（兼容旧数据）
                area_id = menu_plan.area_id
                area = AdministrativeArea.query.get(area_id)
                if area:
                    area_name = area.name

        if not area_id:
            # 从消耗明细的第一个食材的库存获取区域ID（兼容旧数据）
            detail = ConsumptionDetail.query.filter_by(consumption_plan_id=id).first()
            if detail:
                inventory = Inventory.query.filter_by(ingredient_id=detail.ingredient_id).first()
                if inventory:
                    warehouse = Warehouse.query.get(inventory.warehouse_id)
                    if warehouse:
                        area_id = warehouse.area_id
                        # 获取区域名称
                        area = AdministrativeArea.query.get(area_id)
                        if area:
                            area_name = area.name

        # 检查用户是否有权限查看
        if area_id and not current_user.can_access_area_by_id(area_id):
            flash('您没有权限查看该消耗计划', 'danger')
            return redirect(url_for('consumption_plan.index'))

        # 获取消耗明细
        consumption_details = ConsumptionDetail.query.filter_by(consumption_plan_id=id).all()

        # 获取库存状态
        inventory_status = {}
        if consumption_details:
            # 收集所有食材ID
            ingredient_ids = [detail.ingredient_id for detail in consumption_details]

            if ingredient_ids and area_id:
                # 使用原始SQL查询，避免ORM的数据类型问题
                from sqlalchemy import text

                # 构建食材ID列表字符串
                ingredient_id_list = ','.join(str(id) for id in ingredient_ids)

                # 查询库存 - 使用CAST确保数据类型正确
                inventory_query = f"""
                    SELECT i.ingredient_id,
                           CAST(COALESCE(SUM(CAST(i.quantity AS DECIMAL(18, 2))), 0) AS DECIMAL(18, 2)) AS total_quantity
                    FROM inventories i
                    JOIN warehouses w ON i.warehouse_id = w.id
                    WHERE i.ingredient_id IN ({ingredient_id_list})
                    AND w.area_id = :area_id
                    AND i.status = :status
                    GROUP BY i.ingredient_id
                """

                inventory_result = db.session.execute(text(inventory_query), {
                    'area_id': area_id,
                    'status': '正常'
                }).fetchall()

                # 创建库存字典
                inventory_dict = {}
                for inv in inventory_result:
                    inventory_dict[inv.ingredient_id] = float(inv.total_quantity or 0)

                # 设置每个明细的库存状态
                for detail in consumption_details:
                    inventory_total = inventory_dict.get(detail.ingredient_id, 0)
                    inventory_status[detail.id] = {
                        'total': inventory_total,
                        'sufficient': inventory_total >= detail.planned_quantity
                    }
            elif ingredient_ids:
                # 如果没有区域ID，查询所有库存
                from sqlalchemy import text

                # 构建食材ID列表字符串
                ingredient_id_list = ','.join(str(id) for id in ingredient_ids)

                # 查询库存 - 使用CAST确保数据类型正确
                inventory_query = f"""
                    SELECT ingredient_id,
                           CAST(COALESCE(SUM(CAST(quantity AS DECIMAL(18, 2))), 0) AS DECIMAL(18, 2)) AS total_quantity
                    FROM inventories
                    WHERE ingredient_id IN ({ingredient_id_list})
                    AND status = :status
                    GROUP BY ingredient_id
                """

                inventory_result = db.session.execute(text(inventory_query), {
                    'status': '正常'
                }).fetchall()

                # 创建库存字典
                inventory_dict = {}
                for inv in inventory_result:
                    inventory_dict[inv.ingredient_id] = float(inv.total_quantity or 0)

                # 设置每个明细的库存状态
                for detail in consumption_details:
                    inventory_total = inventory_dict.get(detail.ingredient_id, 0)
                    inventory_status[detail.id] = {
                        'total': inventory_total,
                        'sufficient': inventory_total >= detail.planned_quantity
                    }

        # 获取关联的出库单
        stock_out = None
        if consumption_plan.status == '已执行':
            # 直接查询出库单，不使用joinedload
            stock_out = StockOut.query.filter_by(consumption_plan_id=id).first()

            # 记录日志，用于调试
            if stock_out:
                # 预先加载出库单明细的数量，但不加载具体内容
                items_count = stock_out.stock_out_items.count()
                current_app.logger.info(f"找到出库单: {stock_out.stock_out_number}, 明细数量: {items_count}")
            else:
                current_app.logger.warning(f"未找到与消耗计划 {id} 关联的出库单")

        return render_template('consumption_plan/view.html',
                              consumption_plan=consumption_plan,
                              menu_plan=menu_plan,
                              consumption_details=consumption_details,
                              inventory_status=inventory_status,
                              stock_out=stock_out,
                              area_id=area_id,
                              area_name=area_name)

    except Exception as e:
        current_app.logger.error(f"查看消耗计划详情失败: {str(e)}")
        flash(f'查看消耗计划详情失败: {str(e)}', 'danger')
        return redirect(url_for('consumption_plan.index'))

@consumption_plan_bp.route('/consumption-plan/<int:id>/approve', methods=['POST'])
@login_required
def approve(id):
    """审核消耗计划"""
    consumption_plan = ConsumptionPlan.query.get_or_404(id)

    # 获取区域ID - 优先使用消耗计划的 area_id 字段
    area_id = consumption_plan.area_id

    if not area_id and consumption_plan.menu_plan_id:
        # 从菜单计划获取区域ID（兼容旧数据）
        menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)
        if menu_plan:
            area_id = menu_plan.area_id

    if not area_id:
        # 从消耗明细的第一个食材的库存获取区域ID（兼容旧数据）
        detail = ConsumptionDetail.query.filter_by(consumption_plan_id=id).first()
        if detail:
            inventory = Inventory.query.filter_by(ingredient_id=detail.ingredient_id).first()
            if inventory:
                area_id = inventory.warehouse.area_id

    # 检查用户是否有权限审核
    if area_id and not current_user.can_access_area_by_id(area_id):
        flash('您没有权限审核该消耗计划', 'danger')
        return redirect(url_for('consumption_plan.index'))

    # 只有计划中状态的消耗计划可以审核
    if consumption_plan.status != '计划中':
        flash('只有计划中状态的消耗计划可以审核', 'danger')
        return redirect(url_for('consumption_plan.view', id=id))

    # 更新状态
    consumption_plan.status = '已审核'
    consumption_plan.approved_by = current_user.id

    db.session.commit()
    flash('消耗计划审核通过', 'success')
    return redirect(url_for('consumption_plan.view', id=id))

@consumption_plan_bp.route('/consumption-plan/<int:id>/execute', methods=['POST'])
@login_required
def execute(id):
    """执行消耗计划（生成出库单）"""
    try:
        # 使用原始SQL查询消耗计划
        plan_sql = text("""
            SELECT cp.*, mp.area_id as menu_plan_area_id
            FROM consumption_plans cp
            LEFT JOIN menu_plans mp ON cp.menu_plan_id = mp.id
            WHERE cp.id = :id
        """)

        plan = db.session.execute(plan_sql, {'id': id}).fetchone()
        if not plan:
            flash('消耗计划不存在', 'danger')
            return redirect(url_for('consumption_plan.index'))

        # 获取区域ID和仓库ID - 优先使用消耗计划的 area_id 字段
        area_id = plan.area_id or plan.menu_plan_area_id
        warehouse_id = None

        if area_id:
            # 获取该区域的仓库
            warehouse_sql = text("""
                SELECT id FROM warehouses
                WHERE area_id = :area_id AND status = '正常'
            """)
            warehouse = db.session.execute(warehouse_sql, {'area_id': area_id}).fetchone()
            if warehouse:
                warehouse_id = warehouse.id
        else:
            # 从消耗明细的第一个食材的库存获取区域ID
            detail_sql = text("""
                SELECT TOP 1 i.warehouse_id, w.area_id
                FROM consumption_details cd
                JOIN inventories i ON cd.ingredient_id = i.ingredient_id
                JOIN warehouses w ON i.warehouse_id = w.id
                WHERE cd.consumption_plan_id = :plan_id
                AND i.status = '正常'
            """)
            result = db.session.execute(detail_sql, {'plan_id': id}).fetchone()
            if result:
                warehouse_id = result.warehouse_id
                area_id = result.area_id

        # 检查权限
        if area_id and not current_user.can_access_area_by_id(area_id):
            flash('您没有权限执行该消耗计划', 'danger')
            return redirect(url_for('consumption_plan.index'))

        # 检查计划状态
        if plan.status not in ['计划中', '已审核']:
            flash('只能执行状态为"计划中"或"已审核"的消耗计划', 'warning')
            return redirect(url_for('consumption_plan.index'))

        # 获取所有消耗明细
        details_sql = text("""
            SELECT cd.*, i.name as ingredient_name
            FROM consumption_details cd
            JOIN ingredients i ON cd.ingredient_id = i.id
            WHERE cd.consumption_plan_id = :plan_id
        """)
        details = db.session.execute(details_sql, {'plan_id': id}).fetchall()

        # 生成出库单号
        stock_out_number = f"CK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        try:
            # 1. 创建出库单
            stock_out_sql = text("""
                INSERT INTO stock_outs
                (stock_out_number, warehouse_id, consumption_plan_id, stock_out_date,
                 stock_out_type, operator_id, status, notes)
                OUTPUT inserted.id
                VALUES
                (:stock_out_number, :warehouse_id, :consumption_plan_id, GETDATE(),
                 :stock_out_type, :operator_id, :status, :notes)
            """)

            result = db.session.execute(stock_out_sql, {
                'stock_out_number': stock_out_number,
                'warehouse_id': warehouse_id,
                'consumption_plan_id': id,
                'stock_out_type': '消耗出库',
                'operator_id': current_user.id,
                'status': '已出库',
                'notes': f'由消耗计划 #{id} 自动生成'
            })

            stock_out_id = result.fetchone()[0]

            # 2. 批量查询所有需要的库存
            ingredient_ids = [detail.ingredient_id for detail in details]

            # 构建 IN 子句的参数列表
            ingredient_id_list = ','.join(str(id) for id in ingredient_ids)

            inventory_query = text(f"""
                SELECT i.id, i.ingredient_id,
                       CAST(i.quantity AS DECIMAL(18, 2)) as quantity,
                       i.batch_number, i.unit,
                       ing.name as ingredient_name
                FROM inventories i
                JOIN ingredients ing ON i.ingredient_id = ing.id
                WHERE i.ingredient_id IN ({ingredient_id_list})
                AND i.warehouse_id = :warehouse_id
                AND i.status = :status
                AND CAST(i.quantity AS DECIMAL(18, 2)) > :min_quantity
                ORDER BY i.expiry_date
            """)

            inventory_result = db.session.execute(inventory_query, {
                'warehouse_id': warehouse_id,
                'status': '正常',
                'min_quantity': 0.0  # 使用浮点数
            }).fetchall()

            # 创建库存字典，按食材ID分组
            inventory_dict = {}
            for inv in inventory_result:
                if inv.ingredient_id not in inventory_dict:
                    inventory_dict[inv.ingredient_id] = []
                inventory_dict[inv.ingredient_id].append(inv)

            # 3. 批量创建出库单明细
            stock_out_items = []
            inventory_updates = []

            for detail in details:
                # 获取该食材的所有库存记录
                inventory_list = inventory_dict.get(detail.ingredient_id, [])
                if not inventory_list:
                    raise ValueError(f'食材 {detail.ingredient_name} 库存不足')

                # 使用第一条库存记录（按过期日期排序）
                inventory = inventory_list[0]

                # 确保数量都是浮点数
                inventory_quantity = float(inventory.quantity)
                planned_quantity = float(detail.planned_quantity)

                # 检查库存是否足够，如果不足则执行平账入库
                if inventory_quantity < planned_quantity:
                    # 记录库存不足的日志
                    current_app.logger.warning(f"库存不足: 食材={inventory.ingredient_name}, 需要={planned_quantity}, 当前={inventory_quantity}")

                    # 执行平账入库
                    balance_result = create_balance_stock_in(
                        ingredient_id=detail.ingredient_id,
                        warehouse_id=warehouse_id,
                        required_quantity=planned_quantity,
                        current_quantity=inventory_quantity,
                        unit=detail.unit,
                        consumption_plan_id=id,
                        operator_id=current_user.id
                    )

                    if not balance_result['success']:
                        raise ValueError(f'食材 {inventory.ingredient_name} 库存数量不足，平账入库失败: {balance_result["message"]}')

                    # 记录平账信息
                    flash(f'食材 {balance_result["ingredient_name"]} 库存不足，已自动执行平账入库 {balance_result["difference"]} {detail.unit}', 'warning')
                    current_app.logger.info(f"平账入库成功: 食材={balance_result['ingredient_name']}, 补充数量={balance_result['difference']}, 批次号={balance_result.get('batch_number', '未知')}")

                    # 重新查询库存 - 直接使用平账入库创建的批次号
                    batch_number = balance_result.get('batch_number')

                    if batch_number:
                        # 如果有批次号，直接查询该批次的库存
                        inventory_result = db.session.execute(text("""
                            SELECT i.id, i.ingredient_id,
                                   CAST(i.quantity AS DECIMAL(18, 2)) as quantity,
                                   i.batch_number, i.unit,
                                   ing.name as ingredient_name
                            FROM inventories i
                            JOIN ingredients ing ON i.ingredient_id = ing.id
                            WHERE i.batch_number = :batch_number
                            AND i.status = :status
                        """), {
                            'batch_number': batch_number,
                            'status': '正常'
                        }).fetchall()
                    else:
                        # 如果没有批次号，查询该食材的所有库存
                        inventory_result = db.session.execute(text("""
                            SELECT i.id, i.ingredient_id,
                                   CAST(i.quantity AS DECIMAL(18, 2)) as quantity,
                                   i.batch_number, i.unit,
                                   ing.name as ingredient_name
                            FROM inventories i
                            JOIN ingredients ing ON i.ingredient_id = ing.id
                            WHERE i.ingredient_id = :ingredient_id
                            AND i.warehouse_id = :warehouse_id
                            AND i.status = :status
                            AND CAST(i.quantity AS DECIMAL(18, 2)) > :min_quantity
                            ORDER BY i.expiry_date
                        """), {
                            'ingredient_id': detail.ingredient_id,
                            'warehouse_id': warehouse_id,
                            'status': '正常',
                            'min_quantity': 0.0
                        }).fetchall()

                    if not inventory_result:
                        raise ValueError(f'食材 {detail.ingredient_name} 平账入库后仍无法找到库存记录')

                    # 记录找到的库存记录
                    current_app.logger.info(f"平账入库后找到 {len(inventory_result)} 条库存记录")

                    # 使用第一条库存记录（按过期日期排序）
                    inventory = inventory_result[0]

                # 记录使用的库存记录
                current_app.logger.info(f"使用库存记录: ID={inventory.id}, 批次号={inventory.batch_number}, 数量={inventory.quantity}")

                # 准备出库单明细数据
                stock_out_items.append({
                    'stock_out_id': stock_out_id,
                    'inventory_id': inventory.id,
                    'ingredient_id': detail.ingredient_id,
                    'batch_number': inventory.batch_number,
                    'quantity': planned_quantity,
                    'unit': detail.unit,
                    'consumption_detail_id': detail.id,
                    'notes': f'消耗计划出库，详情ID: {detail.id}'
                })

                # 准备库存更新数据
                inventory_updates.append({
                    'id': inventory.id,
                    'quantity': planned_quantity
                })

                # 记录库存更新信息
                current_app.logger.info(f"准备更新库存: ID={inventory.id}, 减少数量={planned_quantity}")

            # 批量插入出库单明细
            if stock_out_items:
                item_sql = text("""
                    INSERT INTO stock_out_items
                    (stock_out_id, inventory_id, ingredient_id, batch_number,
                     quantity, unit, consumption_detail_id, notes)
                    VALUES
                    (:stock_out_id, :inventory_id, :ingredient_id, :batch_number,
                     :quantity, :unit, :consumption_detail_id, :notes)
                """)
                db.session.execute(item_sql, stock_out_items)

            # 批量更新库存
            if inventory_updates:
                # 修改更新逻辑，当库存小于等于0时，自动将状态更新为"已用完"
                update_sql = text("""
                    UPDATE inventories
                    SET quantity = CAST(quantity AS DECIMAL(18, 2)) - :quantity,
                        status = CASE
                            WHEN (CAST(quantity AS DECIMAL(18, 2)) - :quantity) <= 0 THEN '已用完'
                            ELSE status
                        END,
                        updated_at = GETDATE()
                    WHERE id = :id
                """)

                # 记录库存更新操作
                current_app.logger.info(f"执行库存更新: 共 {len(inventory_updates)} 条记录")

                # 执行更新
                db.session.execute(update_sql, inventory_updates)

            # 更新消耗明细状态
            detail_update_sql = text("""
                UPDATE consumption_details
                SET status = '已出库',
                    actual_quantity = planned_quantity,
                    updated_at = GETDATE()
                WHERE consumption_plan_id = :plan_id
            """)
            db.session.execute(detail_update_sql, {'plan_id': id})

            # 更新计划状态
            plan_update_sql = text("""
                UPDATE consumption_plans
                SET status = '已完成',
                    updated_at = GETDATE()
                WHERE id = :plan_id
            """)
            db.session.execute(plan_update_sql, {
                'plan_id': id
            })

            # 提交事务
            db.session.commit()
            flash(f'消耗计划执行成功，出库单号：{stock_out_number}', 'success')

        except Exception as e:
            db.session.rollback()
            raise e

    except ValueError as e:
        flash(str(e), 'danger')
    except Exception as e:
        current_app.logger.error(f"执行消耗计划时出错: {str(e)}")
        flash(f'执行消耗计划时出错: {str(e)}', 'danger')
    finally:
        # 确保关闭数据库连接
        db.session.close()

    return redirect(url_for('consumption_plan.index'))

@consumption_plan_bp.route('/consumption-plan/<int:id>/cancel', methods=['POST'])
@login_required
def cancel(id):
    """取消消耗计划"""
    consumption_plan = ConsumptionPlan.query.get_or_404(id)

    # 获取区域ID - 优先使用消耗计划的 area_id 字段
    area_id = consumption_plan.area_id

    if not area_id and consumption_plan.menu_plan_id:
        # 从菜单计划获取区域ID（兼容旧数据）
        menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)
        if menu_plan:
            area_id = menu_plan.area_id

    if not area_id:
        # 从消耗明细的第一个食材的库存获取区域ID（兼容旧数据）
        detail = ConsumptionDetail.query.filter_by(consumption_plan_id=id).first()
        if detail:
            inventory = Inventory.query.filter_by(ingredient_id=detail.ingredient_id).first()
            if inventory:
                area_id = inventory.warehouse.area_id

    # 检查用户是否有权限取消
    if area_id and not current_user.can_access_area_by_id(area_id):
        flash('您没有权限取消该消耗计划', 'danger')
        return redirect(url_for('consumption_plan.index'))

    # 只有计划中或已审核状态的消耗计划可以取消
    if consumption_plan.status not in ['计划中', '已审核']:
        flash('只有计划中或已审核状态的消耗计划可以取消', 'danger')
        return redirect(url_for('consumption_plan.view', id=id))

    # 更新状态
    consumption_plan.status = '已取消'

    db.session.commit()
    flash('消耗计划已取消', 'success')
    return redirect(url_for('consumption_plan.view', id=id))

@consumption_plan_bp.route('/consumption-plan/new', methods=['GET', 'POST'])
@login_required
def new():
    """直接创建消耗计划（不从菜单计划创建）- 重定向到超级编辑器"""
    return redirect(url_for('consumption_plan_super.super_editor'))

@consumption_plan_bp.route('/consumption-plan/from-weekly-menu')
@login_required
def from_weekly_menu():
    """从周菜单创建消耗计划 - 直接跳转到最新的周菜单"""
    # 获取当前用户的区域
    if not current_user.area_id:
        flash('您没有关联任何学校，无法创建消耗计划', 'danger')
        return redirect(url_for('consumption_plan.index'))

    area_id = current_user.area_id

    # 检查用户是否有权限访问该区域
    if not current_user.can_access_area_by_id(area_id):
        flash('您没有权限访问该区域', 'danger')
        return redirect(url_for('consumption_plan.index'))

    # 获取该区域最新的已发布周菜单
    from app.models import WeeklyMenu
    latest_menu = WeeklyMenu.query.filter_by(
        area_id=area_id,
        status='已发布'
    ).order_by(WeeklyMenu.week_start.desc()).first()

    if not latest_menu:
        flash('当前学校没有已发布的周菜单，请先创建并发布周菜单', 'warning')
        return redirect(url_for('weekly_menu_v2.index'))

    # 直接跳转到最新周菜单的创建页面
    return redirect(url_for('consumption_plan.create_from_weekly', weekly_menu_id=latest_menu.id))

@consumption_plan_bp.route('/consumption-plan/select-weekly-menu')
@login_required
def select_weekly_menu():
    """选择周菜单页面 - 如果用户需要选择其他周菜单时使用"""
    # 获取当前用户的区域
    if not current_user.area_id:
        flash('您没有关联任何学校，无法创建消耗计划', 'danger')
        return redirect(url_for('consumption_plan.index'))

    area_id = current_user.area_id

    # 检查用户是否有权限访问该区域
    if not current_user.can_access_area_by_id(area_id):
        flash('您没有权限访问该区域', 'danger')
        return redirect(url_for('consumption_plan.index'))

    # 获取该区域已发布的周菜单
    from app.models import WeeklyMenu
    weekly_menus = WeeklyMenu.query.filter_by(
        area_id=area_id,
        status='已发布'
    ).order_by(WeeklyMenu.week_start.desc()).limit(10).all()

    if not weekly_menus:
        flash('当前学校没有已发布的周菜单，请先创建并发布周菜单', 'warning')
        return redirect(url_for('weekly_menu_v2.index'))

    # 为每个菜单添加周显示信息和消耗计划状态
    for menu in weekly_menus:
        # 计算周数
        week_number = menu.week_start.isocalendar()[1]
        # 格式化显示
        menu.week_display = f"{menu.week_start.year}年第{week_number}周({menu.week_start.strftime('%m-%d')}至{menu.week_end.strftime('%m-%d')})"

        # 检查是否已经有消耗计划
        existing_consumption_plans = ConsumptionPlan.query.filter(
            ConsumptionPlan.area_id == menu.area_id,
            ConsumptionPlan.consumption_date >= menu.week_start,
            ConsumptionPlan.consumption_date <= menu.week_end
        ).count()

        menu.has_consumption_plans = existing_consumption_plans > 0

    return render_template('consumption_plan/select_weekly_menu.html',
                          title=f'选择周菜单 - {current_user.area.name}',
                          weekly_menus=weekly_menus,
                          current_area=current_user.area)

@consumption_plan_bp.route('/consumption-plan/create-from-weekly/<int:weekly_menu_id>')
@login_required
def create_from_weekly(weekly_menu_id):
    """从指定周菜单创建消耗计划"""
    from app.models import WeeklyMenu, WeeklyMenuRecipe

    # 获取周菜单
    weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)

    # 检查用户是否有权限操作该区域
    if not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限操作该区域', 'danger')
        return redirect(url_for('consumption_plan.from_weekly_menu'))

    # 检查周菜单状态
    if weekly_menu.status != '已发布':
        flash('只有已发布状态的周菜单可以创建消耗计划', 'danger')
        return redirect(url_for('consumption_plan.from_weekly_menu'))

    # 获取周菜单的所有食谱
    weekly_menu_recipes = WeeklyMenuRecipe.query.filter_by(weekly_menu_id=weekly_menu_id).all()

    # 按日期和餐次组织数据
    week_data = {}
    weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

    # 初始化一周的数据结构
    for i in range(7):
        current_date = weekly_menu.week_start + timedelta(days=i)
        date_str = current_date.strftime('%Y-%m-%d')

        week_data[date_str] = {
            'date': date_str,
            'weekday': weekdays[i],
            'meals': {
                '早餐': [],
                '午餐': [],
                '晚餐': []
            }
        }

    # 填充菜单数据
    for recipe in weekly_menu_recipes:
        day_date = weekly_menu.week_start + timedelta(days=recipe.day_of_week - 1)
        date_str = day_date.strftime('%Y-%m-%d')

        if date_str in week_data:
            recipe_data = {
                'id': recipe.id,
                'recipe_id': recipe.recipe_id,
                'name': recipe.recipe_name,
                'ingredients': []
            }

            # 如果有关联的食谱，获取食材信息
            if recipe.recipe_id and recipe.recipe:
                for ri in recipe.recipe.ingredients:
                    recipe_data['ingredients'].append({
                        'id': ri.ingredient_id,
                        'name': ri.ingredient.name,
                        'quantity': ri.quantity,
                        'unit': ri.unit,
                        'category': ri.ingredient.category
                    })

            week_data[date_str]['meals'][recipe.meal_type].append(recipe_data)

    # 获取该区域的仓库
    warehouse = Warehouse.query.filter_by(area_id=weekly_menu.area_id, status='正常').first()
    if not warehouse:
        flash('该区域没有可用的仓库，无法创建消耗计划', 'danger')
        return redirect(url_for('consumption_plan.from_weekly_menu'))

    return render_template('consumption_plan/create_from_weekly.html',
                          title=f'从周菜单创建消耗计划 - {weekly_menu.area.name}',
                          weekly_menu=weekly_menu,
                          week_data=week_data,
                          warehouse=warehouse)

@consumption_plan_bp.route('/consumption-plan/process-weekly-creation', methods=['POST'])
@login_required
def process_weekly_creation():
    """处理从周菜单创建消耗计划的表单提交"""
    from app.models import WeeklyMenu, WeeklyMenuRecipe

    weekly_menu_id = request.form.get('weekly_menu_id', type=int)
    warehouse_id = request.form.get('warehouse_id', type=int)
    selected_meals = request.form.getlist('selected_meals')

    if not weekly_menu_id or not warehouse_id or not selected_meals:
        flash('参数错误，请重新选择', 'danger')
        return redirect(url_for('consumption_plan.from_weekly_menu'))

    # 获取周菜单
    weekly_menu = WeeklyMenu.query.get_or_404(weekly_menu_id)

    # 检查权限
    if not current_user.can_access_area_by_id(weekly_menu.area_id):
        flash('您没有权限操作该区域', 'danger')
        return redirect(url_for('consumption_plan.from_weekly_menu'))

    # 获取仓库
    warehouse = Warehouse.query.get_or_404(warehouse_id)

    created_plans = []
    errors = []

    try:
        for meal_selection in selected_meals:
            # 解析选择的日期和餐次，格式：2025-01-20_午餐
            date_str, meal_type = meal_selection.split('_')
            consumption_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # 检查是否已经存在相同的消耗计划
            existing_plan = ConsumptionPlan.query.filter_by(
                area_id=weekly_menu.area_id,
                consumption_date=consumption_date,
                meal_type=meal_type
            ).first()

            if existing_plan:
                errors.append(f'{date_str} {meal_type} 已存在消耗计划')
                continue

            # 获取该日期和餐次的食谱
            day_of_week = (consumption_date - weekly_menu.week_start).days + 1
            weekly_recipes = WeeklyMenuRecipe.query.filter_by(
                weekly_menu_id=weekly_menu_id,
                day_of_week=day_of_week,
                meal_type=meal_type
            ).all()

            if not weekly_recipes:
                errors.append(f'{date_str} {meal_type} 没有菜单内容')
                continue

            # 创建消耗计划
            consumption_plan = ConsumptionPlan(
                area_id=weekly_menu.area_id,
                consumption_date=consumption_date,
                meal_type=meal_type,
                status='计划中',
                creator_id=current_user.id
            )

            db.session.add(consumption_plan)
            db.session.flush()  # 获取ID

            # 收集所有食材
            ingredient_totals = {}

            for weekly_recipe in weekly_recipes:
                if weekly_recipe.recipe_id and weekly_recipe.recipe:
                    # 从食谱获取食材
                    for recipe_ingredient in weekly_recipe.recipe.ingredients:
                        ingredient_id = recipe_ingredient.ingredient_id
                        quantity = recipe_ingredient.quantity
                        unit = recipe_ingredient.unit

                        if ingredient_id not in ingredient_totals:
                            ingredient_totals[ingredient_id] = {
                                'quantity': 0,
                                'unit': unit,
                                'name': recipe_ingredient.ingredient.name
                            }

                        # 累加数量（假设单位相同）
                        ingredient_totals[ingredient_id]['quantity'] += quantity

            # 创建消耗明细
            for ingredient_id, data in ingredient_totals.items():
                consumption_detail = ConsumptionDetail(
                    consumption_plan_id=consumption_plan.id,
                    ingredient_id=ingredient_id,
                    planned_quantity=data['quantity'],
                    unit=data['unit']
                )
                db.session.add(consumption_detail)

            created_plans.append(f'{date_str} {meal_type}')

        db.session.commit()

        # 显示结果
        if created_plans:
            flash(f'成功创建 {len(created_plans)} 个消耗计划：{", ".join(created_plans)}', 'success')

        if errors:
            flash(f'以下消耗计划创建失败：{"; ".join(errors)}', 'warning')

        return redirect(url_for('consumption_plan.index'))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建消耗计划失败: {str(e)}')
        flash('创建消耗计划失败，请重试', 'danger')
        return redirect(url_for('consumption_plan.create_from_weekly', weekly_menu_id=weekly_menu_id))

@consumption_plan_bp.route('/consumption-plan/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑消耗计划"""
    try:
        # 使用一次查询获取消耗计划及其关联的菜单计划
        consumption_plan = ConsumptionPlan.query.get_or_404(id)

        # 获取区域ID - 优先使用消耗计划的 area_id 字段
        area_id = consumption_plan.area_id
        menu_plan = None

        if not area_id and consumption_plan.menu_plan_id:
            # 从菜单计划获取区域ID（兼容旧数据）
            menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)
            if menu_plan:
                area_id = menu_plan.area_id

        if not area_id:
            # 从消耗明细的第一个食材的库存获取区域ID（兼容旧数据）
            detail = ConsumptionDetail.query.filter_by(consumption_plan_id=id).first()
            if detail:
                inventory = Inventory.query.filter_by(ingredient_id=detail.ingredient_id).first()
                if inventory:
                    warehouse = Warehouse.query.get(inventory.warehouse_id)
                    if warehouse:
                        area_id = warehouse.area_id

        # 检查用户是否有权限编辑
        if area_id and not current_user.can_access_area_by_id(area_id):
            flash('您没有权限编辑该消耗计划', 'danger')
            return redirect(url_for('consumption_plan.index'))

        # 只有计划中状态的消耗计划可以编辑
        if consumption_plan.status != '计划中':
            flash('只有计划中状态的消耗计划可以编辑', 'danger')
            return redirect(url_for('consumption_plan.view', id=id))

        # 获取消耗明细
        consumption_details = ConsumptionDetail.query.filter_by(consumption_plan_id=id).all()

        if request.method == 'POST':
            try:
                # 获取表单数据
                consumption_date = request.form.get('consumption_date')
                meal_type = request.form.get('meal_type')
                diners_count = request.form.get('diners_count', type=int)
                notes = request.form.get('notes')

                # 更新消耗计划
                consumption_plan.consumption_date = datetime.strptime(consumption_date, '%Y-%m-%d').date() if consumption_date else consumption_plan.consumption_date
                consumption_plan.meal_type = meal_type or consumption_plan.meal_type
                consumption_plan.diners_count = diners_count or consumption_plan.diners_count
                consumption_plan.notes = notes

                # 更新消耗明细
                for detail in consumption_details:
                    quantity = request.form.get(f'quantity_{detail.id}', type=float)
                    if quantity and quantity > 0:
                        detail.planned_quantity = quantity

                # 提交事务
                db.session.commit()

                # 检查库存是否足够
                check_inventory(consumption_plan.id, area_id)

                flash('消耗计划更新成功', 'success')
                return redirect(url_for('consumption_plan.view', id=id))

            except Exception as e:
                # 回滚事务
                db.session.rollback()
                current_app.logger.error(f"更新消耗计划失败: {str(e)}")
                flash(f'更新消耗计划失败: {str(e)}', 'danger')

        # 获取仓库信息
        warehouse = None
        if area_id:
            warehouse = Warehouse.query.filter_by(area_id=area_id).first()

        # 获取食材分类
        ingredient_categories = db.session.query(Ingredient.category).distinct().all()

        # 获取库存状态
        inventory_status = {}
        if consumption_details:
            # 收集所有食材ID
            ingredient_ids = [detail.ingredient_id for detail in consumption_details]

            if ingredient_ids and area_id:
                # 一次性查询所有食材的库存
                from sqlalchemy import text

                # 构建食材ID列表字符串
                ingredient_id_list = ','.join(str(id) for id in ingredient_ids)

                # 查询库存
                inventory_query = f"""
                    SELECT i.ingredient_id,
                           CAST(COALESCE(SUM(CAST(i.quantity AS FLOAT)), 0) AS FLOAT) AS total_quantity
                    FROM inventories i
                    JOIN warehouses w ON i.warehouse_id = w.id
                    WHERE i.ingredient_id IN ({ingredient_id_list})
                    AND w.area_id = :area_id
                    AND i.status = :status
                    GROUP BY i.ingredient_id
                """

                inventory_result = db.session.execute(text(inventory_query), {
                    'area_id': area_id,
                    'status': '正常'
                }).fetchall()

                # 创建库存字典
                inventory_dict = {}
                for inv in inventory_result:
                    inventory_dict[inv.ingredient_id] = inv.total_quantity

                # 设置每个明细的库存状态
                for detail in consumption_details:
                    inventory_total = inventory_dict.get(detail.ingredient_id, 0)
                    inventory_status[detail.id] = {
                        'total': inventory_total,
                        'sufficient': inventory_total >= detail.planned_quantity
                    }

        return render_template('consumption_plan/edit.html',
                              consumption_plan=consumption_plan,
                              menu_plan=menu_plan,
                              consumption_details=consumption_details,
                              ingredient_categories=ingredient_categories,
                              warehouse=warehouse,
                              area_id=area_id,
                              inventory_status=inventory_status)

    except Exception as e:
        current_app.logger.error(f"编辑消耗计划页面加载失败: {str(e)}")
        flash(f'编辑消耗计划页面加载失败: {str(e)}', 'danger')
        return redirect(url_for('consumption_plan.index'))

@consumption_plan_bp.route('/consumption-plan/get-warehouses/<int:area_id>')
@login_required
def get_warehouses(area_id):
    """根据区域获取仓库列表"""
    try:
        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            return jsonify({'error': '您没有权限操作该区域'}), 403

        # 获取区域信息
        area = AdministrativeArea.query.get(area_id)
        if not area:
            return jsonify({
                'error': f'未找到ID为{area_id}的区域',
                'message': '请选择有效的区域'
            }), 404

        area_name = area.name

        # 获取该区域的所有仓库
        warehouses = Warehouse.query.filter_by(area_id=area_id, status='正常').all()

        # 转换为JSON格式
        warehouses_data = []
        for warehouse in warehouses:
            warehouses_data.append({
                'id': warehouse.id,
                'name': warehouse.name,
                'location': warehouse.location,
                'manager_id': warehouse.manager_id,
                'status': warehouse.status
            })

        return jsonify({
            'success': True,
            'area_id': area_id,
            'area_name': area_name,
            'warehouses': warehouses_data
        })

    except Exception as e:
        # 记录错误并返回友好的错误信息
        current_app.logger.error(f"获取仓库列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取仓库列表失败',
            'message': str(e)
        }), 500

@consumption_plan_bp.route('/consumption-plan/get-warehouse-inventory/<int:warehouse_id>')
@login_required
def get_warehouse_inventory(warehouse_id):
    """根据仓库ID获取库存食材"""
    try:
        # 获取仓库信息
        warehouse = Warehouse.query.get(warehouse_id)
        if not warehouse:
            return jsonify({
                'error': f'未找到ID为{warehouse_id}的仓库',
                'message': '请选择有效的仓库'
            }), 404

        # 检查用户是否有权限操作该仓库所属的区域
        if not current_user.can_access_area_by_id(warehouse.area_id):
            return jsonify({'error': '您没有权限操作该仓库'}), 403

        # 获取所有食材分类
        categories = IngredientCategory.query.all()

        # 按分类获取库存食材
        from sqlalchemy import func
        result = {}

        for category in categories:
            try:
                # 使用原始SQL查询，避免ORM的数据类型问题
                from sqlalchemy import text

                # 查询该分类下的库存食材
                inventory_query = """
                    SELECT i.ingredient_id, i.unit,
                           CAST(COALESCE(SUM(CAST(i.quantity AS DECIMAL(18, 2))), 0) AS DECIMAL(18, 2)) AS total_quantity,
                           ing.id, ing.name
                    FROM inventories i
                    JOIN ingredients ing ON i.ingredient_id = ing.id
                    WHERE i.warehouse_id = :warehouse_id
                    AND i.status = :status
                    AND ing.category_id = :category_id
                    AND CAST(i.quantity AS DECIMAL(18, 2)) > 0
                    AND ing.is_condiment = 0
                    GROUP BY i.ingredient_id, i.unit, ing.id, ing.name
                """

                inventories = db.session.execute(text(inventory_query), {
                    'warehouse_id': warehouse_id,
                    'status': '正常',
                    'category_id': category.id
                }).fetchall()

                # 转换为JSON格式
                category_data = []
                for inventory in inventories:
                    category_data.append({
                        'ingredient_id': inventory.ingredient_id,
                        'ingredient_name': inventory.name,
                        'unit': inventory.unit,
                        'total_quantity': float(inventory.total_quantity)
                    })

                result[category.id] = {
                    'category_name': category.name,
                    'inventories': category_data
                }
            except Exception as e:
                # 记录单个分类的错误，但继续处理其他分类
                current_app.logger.error(f"处理分类 {category.name} 的库存时出错: {str(e)}")
                result[category.id] = {
                    'category_name': category.name,
                    'inventories': [],
                    'error': f"获取该分类库存时出错: {str(e)}"
                }

        return jsonify(result)

    except Exception as e:
        # 记录错误并返回友好的错误信息
        current_app.logger.error(f"获取库存食材失败: {str(e)}")
        return jsonify({
            'error': '获取库存食材失败',
            'message': str(e)
        }), 500

@consumption_plan_bp.route('/consumption-plan/get-inventory/<int:area_id>')
@login_required
def get_inventory(area_id):
    """根据区域获取库存食材（兼容旧版本）"""
    try:
        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            return jsonify({'error': '您没有权限操作该区域'}), 403

        # 获取区域信息
        area = AdministrativeArea.query.get(area_id)
        if not area:
            return jsonify({
                'error': f'未找到ID为{area_id}的区域',
                'message': '请选择有效的区域'
            }), 404

        area_name = area.name

        # 获取该区域的仓库
        warehouse = Warehouse.query.filter_by(area_id=area_id).first()
        if not warehouse:
            return jsonify({
                'error': f'未找到{area_name}的仓库',
                'message': '请先在仓库管理中为该区域创建仓库',
                'area_id': area_id,
                'area_name': area_name
            }), 404

        # 重定向到仓库库存API
        return get_warehouse_inventory(warehouse.id)

    except Exception as e:
        # 记录错误并返回友好的错误信息
        current_app.logger.error(f"获取库存食材失败: {str(e)}")
        return jsonify({
            'error': '获取库存食材失败',
            'message': str(e)
        }), 500

def check_inventory(consumption_plan_id, area_id, warehouse_id=None):
    """检查库存是否足够，生成库存预警"""
    try:
        # 获取所有消耗明细
        consumption_details = ConsumptionDetail.query.filter_by(consumption_plan_id=consumption_plan_id).all()

        # 如果没有消耗明细，直接返回
        if not consumption_details:
            return

        # 收集所有食材ID
        ingredient_ids = [detail.ingredient_id for detail in consumption_details]

        # 使用原始SQL查询，避免ORM的数据类型问题
        from sqlalchemy import text

        # 构建食材ID列表字符串
        ingredient_id_list = ','.join(str(id) for id in ingredient_ids)

        # 一次性查询所有食材的库存
        if warehouse_id:
            # 如果指定了仓库ID，只检查该仓库的库存
            inventory_query = f"""
                SELECT ingredient_id,
                       CAST(COALESCE(SUM(CAST(quantity AS DECIMAL(18, 2))), 0) AS DECIMAL(18, 2)) AS total_quantity
                FROM inventories
                WHERE ingredient_id IN ({ingredient_id_list})
                AND warehouse_id = :warehouse_id
                AND status = :status
                GROUP BY ingredient_id
            """

            inventory_result = db.session.execute(text(inventory_query), {
                'warehouse_id': warehouse_id,
                'status': '正常'
            }).fetchall()
        else:
            # 否则检查该区域所有仓库的库存
            inventory_query = f"""
                SELECT i.ingredient_id,
                       CAST(COALESCE(SUM(CAST(i.quantity AS DECIMAL(18, 2))), 0) AS DECIMAL(18, 2)) AS total_quantity
                FROM inventories i
                JOIN warehouses w ON i.warehouse_id = w.id
                WHERE i.ingredient_id IN ({ingredient_id_list})
                AND w.area_id = :area_id
                AND i.status = :status
                GROUP BY i.ingredient_id
            """

            inventory_result = db.session.execute(text(inventory_query), {
                'area_id': area_id,
                'status': '正常'
            }).fetchall()

        # 创建库存字典
        inventory_dict = {}
        for inv in inventory_result:
            inventory_dict[inv.ingredient_id] = float(inv.total_quantity or 0)

        # 收集需要创建预警的食材
        alerts_to_create = []

        # 检查每个明细的库存是否足够
        for detail in consumption_details:
            inventory_total = inventory_dict.get(detail.ingredient_id, 0)

            # 如果库存不足，准备生成预警
            if inventory_total < detail.planned_quantity:
                # 检查是否已存在未处理的预警
                existing_alert = InventoryAlert.query.filter_by(
                    ingredient_id=detail.ingredient_id,
                    area_id=area_id,
                    alert_type='库存不足',
                    status='未处理'
                ).first()

                if not existing_alert:
                    alerts_to_create.append({
                        'ingredient_id': detail.ingredient_id,
                        'area_id': area_id,
                        'current_quantity': inventory_total,
                        'unit': detail.unit,
                        'planned_quantity': detail.planned_quantity
                    })

        # 一次性创建所有预警
        if alerts_to_create:
            for alert_data in alerts_to_create:
                alert = InventoryAlert(
                    ingredient_id=alert_data['ingredient_id'],
                    area_id=area_id,
                    current_quantity=alert_data['current_quantity'],
                    unit=alert_data['unit'],
                    alert_type='库存不足',
                    status='未处理',
                    notes=f'消耗计划需要{alert_data["planned_quantity"]}{alert_data["unit"]}，当前库存{alert_data["current_quantity"]}{alert_data["unit"]}'
                )
                db.session.add(alert)

            # 一次性提交所有预警
            db.session.commit()

    except Exception as e:
        # 记录错误但不中断程序
        current_app.logger.error(f"检查库存失败: {str(e)}")
        db.session.rollback()


