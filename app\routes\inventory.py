from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import Inventory, Warehouse, Ingredient, StorageLocation, InventoryAlert
from app.utils.school_required import school_required
from app import db
from datetime import datetime, date, timedelta
from sqlalchemy import func, text
import json

inventory_bp = Blueprint('inventory', __name__)

def get_ingredient_flow_status(inventory):
    """获取食材流转状态信息"""
    try:
        from app.models import StockInItem, StockOutItem

        # 检查是否有出库记录
        has_stock_out = StockOutItem.query.filter_by(
            ingredient_id=inventory.ingredient_id,
            batch_number=inventory.batch_number
        ).first() is not None

        # 检查当前库存数量
        current_quantity = float(inventory.quantity) if inventory.quantity else 0

        # 判断流转状态
        if current_quantity > 0:
            if has_stock_out:
                return {
                    'status': 'partially_consumed',
                    'label': '部分消耗',
                    'class': 'warning',
                    'description': f'剩余 {current_quantity}{inventory.unit}'
                }
            else:
                return {
                    'status': 'available',
                    'label': '有库存',
                    'class': 'success',
                    'description': f'{current_quantity}{inventory.unit}'
                }
        else:
            if has_stock_out:
                return {
                    'status': 'fully_consumed',
                    'label': '已消耗完',
                    'class': 'secondary',
                    'description': '已完全消耗'
                }
            else:
                return {
                    'status': 'empty',
                    'label': '库存为空',
                    'class': 'danger',
                    'description': '无库存记录'
                }

    except Exception as e:
        current_app.logger.error(f"计算食材流转状态时出错: {str(e)}")
        return {
            'status': 'unknown',
            'label': '状态未知',
            'class': 'secondary',
            'description': '无法确定状态'
        }

@inventory_bp.route('/inventory')
@login_required
@school_required
def index(user_area):
    """库存列表页面"""
    # 使用 @school_required 装饰器传入的用户学校区域
    # 获取当前用户可访问的区域（保持兼容性，支持多学校用户）
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    warehouse_id = request.args.get('warehouse_id', type=int)
    ingredient_id = request.args.get('ingredient_id', type=int)
    status = request.args.get('status', '')
    expiry_days = request.args.get('expiry_days', type=int)
    storage_location_id = request.args.get('storage_location_id', type=int)
    view_type = request.args.get('view_type', 'detail')  # 'detail' 或 'summary'
    show_empty = request.args.get('show_empty', '0')  # 是否显示已用完的库存，默认不显示

    # 构建查询
    if view_type == 'detail':
        # 详细视图 - 使用原生SQL查询避免数据类型转换问题
        sql = text("""
            SELECT
                i.id, i.warehouse_id, i.storage_location_id, i.ingredient_id,
                i.batch_number, i.quantity, i.unit, i.production_date, i.expiry_date,
                i.supplier_id, i.status, i.notes, i.created_at, i.updated_at,
                COUNT(*) OVER() as total_count
            FROM
                inventories i
            JOIN
                warehouses w ON w.id = i.warehouse_id
        """)

        # 构建查询条件
        where_clauses = []

        # 处理area_ids参数 - 使用字符串拼接而不是参数绑定
        if len(area_ids) == 1:
            where_clauses.append(f"w.area_id = {area_ids[0]}")
        else:
            area_ids_str = ','.join(str(id) for id in area_ids)
            where_clauses.append(f"w.area_id IN ({area_ids_str})")

        # 其他参数使用命名参数
        params = {}

        if warehouse_id:
            where_clauses.append("i.warehouse_id = :warehouse_id")
            params['warehouse_id'] = warehouse_id
        if ingredient_id:
            where_clauses.append("i.ingredient_id = :ingredient_id")
            params['ingredient_id'] = ingredient_id
        if status:
            where_clauses.append("i.status = :status")
            params['status'] = status
        elif show_empty == '0':  # 如果没有指定状态且不显示已用完的库存
            where_clauses.append("i.status = :normal_status")
            where_clauses.append("i.quantity > :min_quantity")
            params['normal_status'] = '正常'
            params['min_quantity'] = 0.0

            # 记录查询条件
            current_app.logger.info(f"库存查询条件: 状态=正常, 数量>0")
        if expiry_days:
            expiry_date = date.today() + timedelta(days=expiry_days)
            where_clauses.append("i.expiry_date <= :expiry_date")
            params['expiry_date'] = expiry_date
        if storage_location_id:
            where_clauses.append("i.storage_location_id = :storage_location_id")
            params['storage_location_id'] = storage_location_id

        # 添加WHERE子句
        if where_clauses:
            sql = text(sql.text + " WHERE " + " AND ".join(where_clauses))

        # 添加排序和分页
        offset = (page - 1) * per_page
        sql = text(sql.text + f" ORDER BY i.expiry_date OFFSET {offset} ROWS FETCH NEXT {per_page} ROWS ONLY")

        # 执行查询
        result = db.session.execute(sql, params)
        rows = result.fetchall()

        # 计算总记录数
        total_count = rows[0].total_count if rows else 0

        # 创建自定义分页对象
        class CustomPagination:
            def __init__(self, items, page, per_page, total):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total
                self.pages = (total + per_page - 1) // per_page if per_page else 0
                self.has_prev = page > 1
                self.has_next = page < self.pages
                self.prev_num = page - 1
                self.next_num = page + 1

            def iter_pages(self, left_edge=2, left_current=2, right_current=5, right_edge=2):
                last = 0
                for num in range(1, self.pages + 1):
                    if num <= left_edge or \
                       (num > self.page - left_current - 1 and num < self.page + right_current) or \
                       num > self.pages - right_edge:
                        if last + 1 != num:
                            yield None
                        yield num
                        last = num

        # 获取库存记录
        inventories = []
        for row in rows:
            # 创建库存对象
            inventory = Inventory(
                id=row.id,
                warehouse_id=row.warehouse_id,
                storage_location_id=row.storage_location_id,
                ingredient_id=row.ingredient_id,
                batch_number=row.batch_number,
                quantity=row.quantity,
                unit=row.unit,
                production_date=row.production_date,
                expiry_date=row.expiry_date,
                supplier_id=row.supplier_id,
                status=row.status,
                notes=row.notes,
                created_at=row.created_at,
                updated_at=row.updated_at
            )

            # 加载关联对象
            inventory.warehouse = Warehouse.query.get(row.warehouse_id)
            inventory.storage_location = StorageLocation.query.get(row.storage_location_id)
            inventory.ingredient = Ingredient.query.get(row.ingredient_id)

            # 计算食材流转状态
            inventory.flow_status = get_ingredient_flow_status(inventory)

            inventories.append(inventory)

        # 创建分页对象
        pagination = CustomPagination(inventories, page, per_page, total_count)

        # 获取仓库列表
        warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).all()

        # 获取食材列表
        ingredients = Ingredient.query.all()

        # 获取存储位置列表
        storage_locations = []
        if warehouse_id:
            storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse_id).all()

        return render_template('inventory/index.html',
                              inventories=inventories,
                              pagination=pagination,
                              warehouses=warehouses,
                              ingredients=ingredients,
                              storage_locations=storage_locations,
                              warehouse_id=warehouse_id,
                              ingredient_id=ingredient_id,
                              status=status,
                              expiry_days=expiry_days,
                              storage_location_id=storage_location_id,
                              view_type=view_type,
                              show_empty=show_empty)
    else:
        # 汇总视图 - 按食材汇总库存
        # 使用 SQLAlchemy 的 func 进行分组查询
        # 使用原始SQL语句进行查询，避免对nvarchar(max)类型使用聚合函数
        # 构建SQL查询
        sql_base = """
        SELECT
            inventories.ingredient_id AS inventories_ingredient_id,
            ingredients.name AS ingredient_name,
            ingredients.category AS ingredient_category,
            SUM(CAST(inventories.quantity AS DECIMAL(18, 2))) AS total_quantity,
            MAX(CAST(inventories.unit AS nvarchar(50))) AS unit
        FROM
            inventories
            JOIN ingredients ON ingredients.id = inventories.ingredient_id
            JOIN warehouses ON warehouses.id = inventories.warehouse_id
        WHERE
        """

        # 处理area_ids参数 - 使用字符串拼接
        if len(area_ids) == 1:
            area_condition = f" warehouses.area_id = {area_ids[0]}"
        else:
            area_ids_str = ','.join(str(id) for id in area_ids)
            area_condition = f" warehouses.area_id IN ({area_ids_str})"

        # 添加状态和数量条件
        status_value = status or '正常'
        # 如果状态是"正常"，则只显示数量大于0的记录
        min_quantity_value = 0 if show_empty == '1' or status == '已用完' else 0.001

        # 记录查询条件
        current_app.logger.info(f"库存汇总查询条件: 状态={status_value}, 最小数量={min_quantity_value}")

        sql_conditions = f"{area_condition} AND inventories.status = :status AND inventories.quantity > :min_quantity"

        # 完整SQL
        sql_full = sql_base + sql_conditions + """
        GROUP BY
            inventories.ingredient_id, ingredients.name, ingredients.category
        ORDER BY
            ingredients.name
        """

        sql = text(sql_full)

        # 构建参数
        params = {
            'status': status_value,
            'min_quantity': min_quantity_value
        }

        # 添加额外的过滤条件
        additional_conditions = []
        if warehouse_id:
            additional_conditions.append("inventories.warehouse_id = :warehouse_id")
            params['warehouse_id'] = warehouse_id
        if ingredient_id:
            additional_conditions.append("inventories.ingredient_id = :ingredient_id")
            params['ingredient_id'] = ingredient_id
        if storage_location_id:
            additional_conditions.append("inventories.storage_location_id = :storage_location_id")
            params['storage_location_id'] = storage_location_id

        # 如果有额外的过滤条件，修改SQL语句
        if additional_conditions:
            additional_where = " AND " + " AND ".join(additional_conditions)
            # 在GROUP BY前添加额外条件
            sql_parts = sql.text.split("GROUP BY")
            sql_with_where = sql_parts[0] + additional_where + " GROUP BY" + sql_parts[1]
            sql = text(sql_with_where)

        # 执行查询
        result = db.session.execute(sql, params)
        inventory_summary = result.fetchall()



        # 获取仓库列表
        warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).all()

        # 获取食材列表
        ingredients = Ingredient.query.all()

        # 获取存储位置列表
        storage_locations = []
        if warehouse_id:
            storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse_id).all()

        return render_template('inventory/summary.html',
                              inventory_summary=inventory_summary,
                              warehouses=warehouses,
                              ingredients=ingredients,
                              storage_locations=storage_locations,
                              warehouse_id=warehouse_id,
                              ingredient_id=ingredient_id,
                              storage_location_id=storage_location_id,
                              view_type=view_type,
                              show_empty=show_empty)

@inventory_bp.route('/inventory/detail/<int:id>')
@login_required
def detail(id):
    """查看库存详情"""
    try:
        # 获取库存记录
        inventory = Inventory.query.get_or_404(id)

        # 检查用户是否有权限查看
        if not current_user.can_access_area_by_id(inventory.warehouse.area_id):
            flash('您没有权限查看该库存', 'danger')
            return redirect(url_for('inventory.index'))

        # 导入溯源相关模型
        from app.models import StockInItem, StockIn, StockOutItem, ConsumptionPlan, StockInDocument
        from app.models_ingredient_traceability import MaterialBatch, TraceDocument, BatchFlow

        # 通过批次号查询入库明细
        stock_in_items = StockInItem.query.filter_by(batch_number=inventory.batch_number).all()

        # 获取入库单
        stock_ins = []
        if stock_in_items:
            for item in stock_in_items:
                if item.stock_in and item.stock_in not in stock_ins:
                    stock_ins.append(item.stock_in)

        # 获取检验检疫证明
        certificates = []
        for stock_in in stock_ins:
            try:
                if hasattr(stock_in, 'documents') and stock_in.documents:
                    for doc in stock_in.documents:
                        if doc not in certificates:
                            certificates.append(doc)
            except Exception as e:
                current_app.logger.error(f"获取检验检疫证明失败: {str(e)}")

        # 获取出库记录和关联的消耗计划
        stock_out_items = []
        consumption_plans = []
        try:
            # 获取使用该批次号的出库明细
            stock_out_items = StockOutItem.query.filter_by(batch_number=inventory.batch_number).all()
            for item in stock_out_items:
                if item.stock_out and item.stock_out.consumption_plan and item.stock_out.consumption_plan not in consumption_plans:
                    consumption_plans.append(item.stock_out.consumption_plan)
        except Exception as e:
            current_app.logger.error(f"获取出库记录和关联消耗计划失败: {str(e)}")

        # 获取溯源信息
        material_batch = None
        trace_documents = []
        batch_flows = []
        try:
            # 通过批次号查询溯源批次
            material_batch = MaterialBatch.query.filter_by(batch_number=inventory.batch_number).first()
            if material_batch:
                # 获取溯源文档
                trace_documents = TraceDocument.query.filter_by(batch_id=material_batch.id).all()
                # 获取批次流水
                batch_flows = BatchFlow.query.filter_by(batch_id=material_batch.id).all()
        except Exception as e:
            current_app.logger.error(f"获取溯源信息失败: {str(e)}")

        # 记录日志
        current_app.logger.info(f"查看库存详情: ID={id}, 批次号={inventory.batch_number}")

        return render_template('inventory/detail.html',
                              inventory=inventory,
                              stock_in_items=stock_in_items,
                              stock_ins=stock_ins,
                              certificates=certificates,
                              stock_out_items=stock_out_items,
                              consumption_plans=consumption_plans,
                              material_batch=material_batch,
                              trace_documents=trace_documents,
                              batch_flows=batch_flows)
    except Exception as e:
        current_app.logger.error(f"查看库存详情失败: {str(e)}")
        flash(f'查看库存详情失败: {str(e)}', 'danger')
        return redirect(url_for('inventory.index'))

@inventory_bp.route('/inventory/get-storage-locations')
@login_required
def get_storage_locations():
    """获取仓库的存储位置列表（AJAX）"""
    warehouse_id = request.args.get('warehouse_id', type=int)

    if not warehouse_id:
        return jsonify([])

    # 获取存储位置列表
    storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse_id, status='正常').all()

    # 转换为JSON格式
    locations = [{'id': loc.id, 'name': f"{loc.name} ({loc.location_code})"} for loc in storage_locations]

    return jsonify(locations)

@inventory_bp.route('/inventory/ingredient/<int:id>')
@login_required
@school_required
def ingredient_inventory(id, user_area):
    """查看某个食材的库存情况"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取食材信息
    ingredient = Ingredient.query.get_or_404(id)

    # 获取查询参数
    show_empty = request.args.get('show_empty', '0')

    # 使用原生SQL查询避免数据类型转换问题
    sql = text("""
        SELECT
            i.id, i.warehouse_id, i.storage_location_id, i.ingredient_id,
            i.batch_number, i.quantity, i.unit, i.production_date, i.expiry_date,
            i.supplier_id, i.status, i.notes, i.created_at, i.updated_at
        FROM
            inventories i
        JOIN
            warehouses w ON w.id = i.warehouse_id
        WHERE
            i.ingredient_id = :ingredient_id
    """)

    # 处理area_ids参数 - 使用字符串拼接
    if len(area_ids) == 1:
        sql = text(sql.text + f" AND w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        sql = text(sql.text + f" AND w.area_id IN ({area_ids_str})")

    # 构建查询参数
    params = {
        'ingredient_id': id
    }

    # 根据参数决定是否显示已用完的库存
    if show_empty == '0':
        sql = text(sql.text + """
            AND i.status = :status
            AND i.quantity > :min_quantity
        """)
        params['status'] = '正常'
        params['min_quantity'] = 0.0

    # 添加排序
    sql = text(sql.text + " ORDER BY i.expiry_date")

    # 执行查询
    result = db.session.execute(sql, params)
    rows = result.fetchall()

    # 获取该食材的库存列表
    inventories = []
    for row in rows:
        # 创建库存对象
        inventory = Inventory(
            id=row.id,
            warehouse_id=row.warehouse_id,
            storage_location_id=row.storage_location_id,
            ingredient_id=row.ingredient_id,
            batch_number=row.batch_number,
            quantity=row.quantity,
            unit=row.unit,
            production_date=row.production_date,
            expiry_date=row.expiry_date,
            supplier_id=row.supplier_id,
            status=row.status,
            notes=row.notes,
            created_at=row.created_at,
            updated_at=row.updated_at
        )

        # 加载关联对象
        inventory.warehouse = Warehouse.query.get(row.warehouse_id)
        inventory.storage_location = StorageLocation.query.get(row.storage_location_id)
        inventory.ingredient = Ingredient.query.get(row.ingredient_id)

        inventories.append(inventory)

    # 计算总库存 - 确保将字符串转换为浮点数
    total_quantity = sum(float(inv.quantity) if isinstance(inv.quantity, str) else inv.quantity for inv in inventories)

    # 获取该食材的库存预警设置
    alerts = InventoryAlert.query.filter_by(ingredient_id=id).all()

    return render_template('inventory/ingredient.html',
                          ingredient=ingredient,
                          inventories=inventories,
                          total_quantity=total_quantity,
                          alerts=alerts,
                          show_empty=show_empty)

@inventory_bp.route('/inventory/check-expiry')
@login_required
@school_required
def check_expiry(user_area):
    """检查临期和过期库存"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    days = request.args.get('days', 7, type=int)  # 默认7天内过期
    warehouse_id = request.args.get('warehouse_id', type=int)

    # 计算临期日期
    expiry_date = date.today() + timedelta(days=days)

    # 使用原生SQL查询临期库存，避免数据类型转换问题
    sql_expiring = text("""
        SELECT
            i.id, i.warehouse_id, i.storage_location_id, i.ingredient_id,
            i.batch_number, i.quantity, i.unit, i.production_date, i.expiry_date,
            i.supplier_id, i.status, i.notes, i.created_at, i.updated_at
        FROM
            inventories i
        JOIN
            warehouses w ON w.id = i.warehouse_id
        WHERE
    """)

    # 处理area_ids参数 - 使用字符串拼接
    if len(area_ids) == 1:
        sql_expiring = text(sql_expiring.text + f" w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        sql_expiring = text(sql_expiring.text + f" w.area_id IN ({area_ids_str})")

    # 添加其他条件
    sql_expiring = text(sql_expiring.text + """
            AND i.status = :status
            AND i.quantity > :min_quantity
            AND CONVERT(DATE, i.expiry_date) <= CONVERT(DATE, :expiry_date)
    """)

    # 构建查询参数 - 将日期转换为字符串格式
    params_expiring = {
        'status': '正常',
        'min_quantity': 0.0,
        'expiry_date': expiry_date.strftime('%Y-%m-%d')
    }

    # 应用仓库过滤
    if warehouse_id:
        sql_expiring = text(sql_expiring.text + " AND i.warehouse_id = :warehouse_id")
        params_expiring['warehouse_id'] = warehouse_id

    # 添加排序
    sql_expiring = text(sql_expiring.text + " ORDER BY i.expiry_date")

    # 执行查询
    result_expiring = db.session.execute(sql_expiring, params_expiring)
    rows_expiring = result_expiring.fetchall()

    # 获取临期库存列表
    expiring_inventories = []
    for row in rows_expiring:
        # 创建库存对象
        inventory = Inventory(
            id=row.id,
            warehouse_id=row.warehouse_id,
            storage_location_id=row.storage_location_id,
            ingredient_id=row.ingredient_id,
            batch_number=row.batch_number,
            quantity=row.quantity,
            unit=row.unit,
            production_date=row.production_date,
            expiry_date=row.expiry_date,
            supplier_id=row.supplier_id,
            status=row.status,
            notes=row.notes,
            created_at=row.created_at,
            updated_at=row.updated_at
        )

        # 加载关联对象
        inventory.warehouse = Warehouse.query.get(row.warehouse_id)
        inventory.storage_location = StorageLocation.query.get(row.storage_location_id)
        inventory.ingredient = Ingredient.query.get(row.ingredient_id)

        # 计算剩余天数
        try:
            if isinstance(row.expiry_date, str):
                expiry_date_obj = datetime.strptime(row.expiry_date, '%Y-%m-%d').date()
            else:
                expiry_date_obj = row.expiry_date

            days_remaining = (expiry_date_obj - date.today()).days
            inventory.days_remaining = max(0, days_remaining)  # 确保不为负数
        except:
            inventory.days_remaining = 0

        expiring_inventories.append(inventory)

    # 使用原生SQL查询已过期库存，避免数据类型转换问题
    sql_expired = text("""
        SELECT
            i.id, i.warehouse_id, i.storage_location_id, i.ingredient_id,
            i.batch_number, i.quantity, i.unit, i.production_date, i.expiry_date,
            i.supplier_id, i.status, i.notes, i.created_at, i.updated_at
        FROM
            inventories i
        JOIN
            warehouses w ON w.id = i.warehouse_id
        WHERE
    """)

    # 处理area_ids参数 - 使用字符串拼接
    if len(area_ids) == 1:
        sql_expired = text(sql_expired.text + f" w.area_id = {area_ids[0]}")
    else:
        area_ids_str = ','.join(str(id) for id in area_ids)
        sql_expired = text(sql_expired.text + f" w.area_id IN ({area_ids_str})")

    # 添加其他条件
    sql_expired = text(sql_expired.text + """
            AND i.status = :status
            AND i.quantity > :min_quantity
            AND CONVERT(DATE, i.expiry_date) < CONVERT(DATE, :today)
    """)

    # 构建查询参数 - 将日期转换为字符串格式
    params_expired = {
        'status': '正常',
        'min_quantity': 0.0,
        'today': date.today().strftime('%Y-%m-%d')
    }

    # 应用仓库过滤
    if warehouse_id:
        sql_expired = text(sql_expired.text + " AND i.warehouse_id = :warehouse_id")
        params_expired['warehouse_id'] = warehouse_id

    # 添加排序
    sql_expired = text(sql_expired.text + " ORDER BY i.expiry_date")

    # 执行查询
    result_expired = db.session.execute(sql_expired, params_expired)
    rows_expired = result_expired.fetchall()

    # 获取已过期库存列表
    expired_inventories = []
    for row in rows_expired:
        # 创建库存对象
        inventory = Inventory(
            id=row.id,
            warehouse_id=row.warehouse_id,
            storage_location_id=row.storage_location_id,
            ingredient_id=row.ingredient_id,
            batch_number=row.batch_number,
            quantity=row.quantity,
            unit=row.unit,
            production_date=row.production_date,
            expiry_date=row.expiry_date,
            supplier_id=row.supplier_id,
            status=row.status,
            notes=row.notes,
            created_at=row.created_at,
            updated_at=row.updated_at
        )

        # 加载关联对象
        inventory.warehouse = Warehouse.query.get(row.warehouse_id)
        inventory.storage_location = StorageLocation.query.get(row.storage_location_id)
        inventory.ingredient = Ingredient.query.get(row.ingredient_id)

        # 计算过期天数
        try:
            if isinstance(row.expiry_date, str):
                expiry_date_obj = datetime.strptime(row.expiry_date, '%Y-%m-%d').date()
            else:
                expiry_date_obj = row.expiry_date

            days_expired = (date.today() - expiry_date_obj).days
            inventory.days_expired = days_expired
        except:
            inventory.days_expired = 0

        expired_inventories.append(inventory)

    # 获取仓库列表
    warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).all()

    return render_template('inventory/expiry.html',
                          expiring_inventories=expiring_inventories,
                          expired_inventories=expired_inventories,
                          days=days,
                          warehouse_id=warehouse_id,
                          warehouses=warehouses)
