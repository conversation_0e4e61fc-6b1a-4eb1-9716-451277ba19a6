{% extends 'base.html' %}

{% block title %}从周菜单创建采购订单 - {{ super() }}{% endblock %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/sweetalert2/css/bootstrap-4.css') }}">
<script src="{{ url_for('static', filename='vendor/sweetalert2/sweetalert2.min.js') }}"></script>
{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* 用户引导样式 */
.context-guidance {
    border-left: 4px solid #17a2b8;
    background-color: #f8f9fa;
}
.workflow-context {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}
.previous-step, .current-step, .next-step {
    flex: 1;
    padding: 0 10px;
}
.current-step {
    border-left: 1px solid #dee2e6;
    border-right: 1px solid #dee2e6;
}
.guidance-tips li {
    margin-bottom: 5px;
}
.step-guide-card {
    margin-bottom: 20px;
}
.alert-icon {
    font-size: 1.2rem;
    margin-right: 10px;
}
.process-step {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    position: relative;
}
.process-step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -20px;
    width: 20px;
    height: 2px;
    background-color: #dee2e6;
}
.process-step:first-child::before {
    display: none;
}
.step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: #17a2b8;
    color: white;
    border-radius: 50%;
    margin-right: 10px;
}
.highlight-box {
    border-left: 4px solid #28a745;
    padding-left: 15px;
    margin-bottom: 15px;
}

/* 原有样式 */
.meal-items .badge {
    margin-right: 5px;
    margin-bottom: 5px;
}
.recipe-info {
    cursor: pointer;
    margin-left: 3px;
}
.ingredients-preview .card {
    padding: 15px;
    margin-bottom: 15px;
}
.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.05);
}
.selected-row {
    background-color: rgba(0,123,255,.1) !important;
}
.meal-count {
    font-size: 0.85em;
}
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
.quantity-input {
    font-size: 1.2em !important;
    font-weight: bold !important;
    color: #dc3545 !important;
    text-align: center !important;
    background-color: #fff3f3 !important;
    border: 2px solid #dc3545 !important;
}
.quantity-input:focus {
    background-color: #fff !important;
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220,53,69,.25) !important;
}
.supplier-select {
    font-size: 1.1em !important;
}
.ingredient-name {
    font-weight: bold;
    color: #495057;
}
.unit-label {
    font-weight: 500;
    color: #6c757d;
}
.card-header {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
}
.card-header h6 {
    color: #495057;
    font-weight: bold;
    font-size: 1.1em;
}
.table thead th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* 餐次容器样式 */
.meal-container {
    width: 100%;
}

/* 无菜单容器 */
.no-menu-container {
    padding: 20px 8px;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #dee2e6;
}

.no-menu-container .text-muted {
    font-size: 15px;
    color: #6c757d !important;
}

/* 餐次标题区域 */
.meal-header {
    margin-bottom: 8px;
}

.form-check {
    background-color: #f8f9fa;
    padding: 6px 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    margin-bottom: 0;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    font-size: 15px;
}

/* 菜谱网格布局 - 两列显示 */
.recipes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px 10px;
    margin-top: 10px;
}

.recipe-item {
    padding: 4px 0;
    font-size: 15px;
    color: #495057;
    line-height: 1.4;
    word-break: break-word;
}

.recipe-item i {
    margin-right: 4px;
    font-size: 12px;
    color: #6c757d;
}

/* 表格样式 */
.table td {
    vertical-align: top;
    padding: 10px 6px;
    min-width: 180px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    text-align: center;
    padding: 8px 6px;
}

/* 让表格更紧凑 */
.table-responsive {
    font-size: 15px;
}

/* 优化复选框样式 */
.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* 让日期列更突出 */
.table td:first-child {
    font-weight: 600;
    color: #495057;
    text-align: center;
    min-width: 80px;
}

/* 星期列样式 */
.table td:nth-child(2) {
    text-align: center;
    min-width: 60px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .recipes-grid {
        grid-template-columns: 1fr;
        gap: 4px;
    }

    .table td {
        min-width: 120px;
        padding: 8px 4px;
    }

    .recipe-item {
        font-size: 15px;
    }

    .form-check-label {
        font-size: 15px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 采购计划流程引导 -->
    <div class="context-guidance card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> 采购计划创建 - 流程指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle alert-icon"></i> <strong>提示：</strong> 采购计划是食堂管理的重要环节，合理的采购计划可以确保食材供应充足，同时避免浪费。
            </div>

            <div class="workflow-context mt-3">
                <div class="previous-step">
                    <small class="text-muted">上一步</small>
                    <p><i class="fas fa-calendar-alt"></i> 周菜单计划</p>
                    <small>已完成周菜单的制定</small>
                    <div class="mt-2">
                        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回菜单计划
                        </a>
                    </div>
                </div>
                <div class="current-step bg-light p-2 border rounded">
                    <small class="text-muted">当前步骤</small>
                    <p class="font-weight-bold"><i class="fas fa-shopping-cart"></i> 创建采购计划</p>
                    <small>根据菜单生成采购清单</small>
                </div>
                <div class="next-step">
                    <small class="text-muted">下一步</small>
                    <p><i class="fas fa-clipboard-check"></i> 入库检查</p>
                    <small>对采购的食材进行检查</small>
                    <div class="mt-2">
                        <a href="{{ url_for('inspection.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> 前往入库检查
                        </a>
                    </div>
                </div>
            </div>

            <!-- 操作流程指南 -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-tasks"></i> 采购计划创建流程</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="process-step">
                                <span class="step-number">1</span>
                                <strong>选择日期</strong>
                                <p class="small text-muted mt-2 mb-0">选择需要采购食材的日期，可以选择多天一起采购</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="process-step">
                                <span class="step-number">2</span>
                                <strong>生成食材清单</strong>
                                <p class="small text-muted mt-2 mb-0">系统会根据菜单自动计算所需食材的种类和数量</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="process-step">
                                <span class="step-number">3</span>
                                <strong>确认采购信息</strong>
                                <p class="small text-muted mt-2 mb-0">调整采购数量，选择供应商，确认后生成采购订单</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作提示 -->
            <div class="alert alert-light border mt-3">
                <h6 class="alert-heading"><i class="fas fa-lightbulb text-warning"></i> 操作提示</h6>
                <ul class="mb-0">
                    <li>勾选需要采购的日期（可以选择多天一起采购，减少采购频次）</li>
                    <li>点击"生成食材清单"按钮，系统会自动计算所需食材</li>
                    <li>在弹出的窗口中，可以调整采购数量和选择供应商</li>
                    <li>确认无误后，点击"确认采购"按钮生成采购订单</li>
                    <li>生成的采购订单可以打印或发送给供应商</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>{{ area.name }}采购订单创建</h2>
            <p class="text-muted">{{ week_start }} 至 {{ week_end }}</p>
        </div>
        <div class="col-md-4 text-right">
            <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>

    <!-- 周菜单选择卡片 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-utensils"></i> 选择要创建采购计划的日期和餐次
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="thead-light">
                        <tr>
                            <th style="width: 100px;">日期</th>
                            <th style="width: 80px;">星期</th>
                            <th>早餐</th>
                            <th>午餐</th>
                            <th>晚餐</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for date_str, day_data in week_data.items() %}
                        <tr>
                            <td>
                                <strong>{{ date_str[5:] }}</strong>
                            </td>
                            <td>
                                <span class="badge badge-secondary">{{ day_data.weekday }}</span>
                            </td>

                            <!-- 早餐 -->
                            <td>
                                {% if day_data.meals['早餐'] %}
                                <div class="meal-container">
                                    <div class="meal-header">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="selected_meals"
                                                   value="{{ date_str }}_早餐"
                                                   id="meal_{{ date_str }}_breakfast">
                                            <label class="form-check-label" for="meal_{{ date_str }}_breakfast">
                                                <strong>创建早餐采购</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="recipes-grid">
                                        {% for recipe in day_data.meals['早餐'] %}
                                        <div class="recipe-item">
                                            <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% else %}
                                <div class="no-menu-container">
                                    <span class="text-muted">
                                        <i class="fas fa-minus-circle"></i> 无菜单安排
                                    </span>
                                </div>
                                {% endif %}
                            </td>

                            <!-- 午餐 -->
                            <td>
                                {% if day_data.meals['午餐'] %}
                                <div class="meal-container">
                                    <div class="meal-header">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="selected_meals"
                                                   value="{{ date_str }}_午餐"
                                                   id="meal_{{ date_str }}_lunch">
                                            <label class="form-check-label" for="meal_{{ date_str }}_lunch">
                                                <strong>创建午餐采购</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="recipes-grid">
                                        {% for recipe in day_data.meals['午餐'] %}
                                        <div class="recipe-item">
                                            <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% else %}
                                <div class="no-menu-container">
                                    <span class="text-muted">
                                        <i class="fas fa-minus-circle"></i> 无菜单安排
                                    </span>
                                </div>
                                {% endif %}
                            </td>

                            <!-- 晚餐 -->
                            <td>
                                {% if day_data.meals['晚餐'] %}
                                <div class="meal-container">
                                    <div class="meal-header">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="selected_meals"
                                                   value="{{ date_str }}_晚餐"
                                                   id="meal_{{ date_str }}_dinner">
                                            <label class="form-check-label" for="meal_{{ date_str }}_dinner">
                                                <strong>创建晚餐采购</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="recipes-grid">
                                        {% for recipe in day_data.meals['晚餐'] %}
                                        <div class="recipe-item">
                                            <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% else %}
                                <div class="no-menu-container">
                                    <span class="text-muted">
                                        <i class="fas fa-minus-circle"></i> 无菜单安排
                                    </span>
                                </div>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 批量选择按钮 -->
            <div class="mt-3">
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                    <i class="fas fa-check-square"></i> 全选
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm ml-2" onclick="selectNone()">
                    <i class="fas fa-square"></i> 全不选
                </button>
                <button type="button" class="btn btn-outline-info btn-sm ml-2" onclick="selectLunchOnly()">
                    <i class="fas fa-sun"></i> 只选午餐
                </button>
                <button type="button" class="btn btn-outline-success btn-sm ml-2" onclick="analyzeSelected()">
                    <i class="fas fa-search"></i> 分析选中餐次
                </button>
            </div>

            <!-- 分析结果显示区域 -->
            <div id="analysis-result" class="mt-3" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line"></i> 选中餐次分析结果
                        </h6>
                    </div>
                    <div class="card-body" id="analysis-content">
                        <!-- 动态内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">加载中...</span>
    </div>
  </div>
</div>

<!-- 食材预览模态框 -->
<div class="modal fade" id="ingredientsPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title"><i class="fas fa-list-alt"></i> 食材采购清单</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>

            <!-- 操作指引 -->
            <div class="alert alert-light border-info m-3">
                <div class="d-flex">
                    <div class="mr-3">
                        <i class="fas fa-info-circle text-info fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="alert-heading">食材采购清单操作指引</h6>
                        <p>在此页面您可以：</p>
                        <ol>
                            <li><strong>调整采购数量</strong> - 根据实际需求和库存情况调整每种食材的采购数量</li>
                            <li><strong>选择计量单位</strong> - 选择适合的计量单位（公斤、箱、包等）</li>
                            <li><strong>选择供应商</strong> - 为每种食材选择合适的供应商或设置为自购</li>
                        </ol>
                        <div class="alert alert-warning py-2">
                            <small><i class="fas fa-exclamation-triangle"></i> <strong>注意：</strong> 所有食材必须设置大于0的采购数量，并选择供应商才能提交。</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-body">
                <!-- 这里将通过JavaScript动态填充内容 -->
            </div>

            <!-- 采购提示 -->
            <div class="alert alert-light border-success mx-3 mb-3">
                <h6 class="text-success"><i class="fas fa-lightbulb"></i> 采购建议</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="small mb-0">
                            <li>蔬菜类食材建议少量多次采购，保证新鲜度</li>
                            <li>肉类和冷冻食品可以适当增加采购量，减少采购频次</li>
                            <li>干货类食材可以批量采购，降低采购成本</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="small mb-0">
                            <li>采购前先检查库存，避免重复采购</li>
                            <li>考虑食材的保质期，避免过期浪费</li>
                            <li>根据季节性调整采购策略，选择当季食材</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-arrow-left"></i> 返回修改
                </button>
                <button type="button" class="btn btn-primary btn-lg" id="confirmPurchaseBtn">
                    <i class="fas fa-shopping-cart"></i> 确认采购
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// 全局变量
let weeklyMenuId = null;
let areaId = null;

// 页面加载完成后执行
$(document).ready(function() {
    // 从URL或页面数据中获取周菜单ID和区域ID
    const urlParams = new URLSearchParams(window.location.search);
    weeklyMenuId = urlParams.get('weekly_menu_id') || {{ weekly_menu.id if weekly_menu else 'null' }};
    areaId = {{ area.id if area else 'null' }};

    console.log('周菜单ID:', weeklyMenuId);
    console.log('区域ID:', areaId);
});

// 选择功能
function selectAll() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = true;
    });
}

function selectNone() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = false;
    });
}

function selectLunchOnly() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = checkbox.value.includes('_午餐');
    });
}

// 分析选中餐次
function analyzeSelected() {
    // 获取选中的餐次
    const selectedMeals = [];
    document.querySelectorAll('input[name="selected_meals"]:checked').forEach(function(checkbox) {
        selectedMeals.push(checkbox.value);
    });

    if (selectedMeals.length === 0) {
        alert('请先选择要分析的餐次');
        return;
    }

    // 显示加载状态
    const analysisResult = document.getElementById('analysis-result');
    const analysisContent = document.getElementById('analysis-content');

    analysisResult.style.display = 'block';
    analysisContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在分析选中餐次...</div>';

    // 发送AJAX请求
    fetch('/purchase-order/analyze-selected-meals', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            weekly_menu_id: weeklyMenuId,
            area_id: areaId,
            selected_meals: selectedMeals
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAnalysisResult(data.data);
        } else {
            analysisContent.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + data.message + '</div>';
        }
    })
    .catch(error => {
        console.error('分析失败:', error);
        analysisContent.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> 分析失败，请重试</div>';
    });
}

// 显示分析结果
function displayAnalysisResult(data) {
    const analysisContent = document.getElementById('analysis-content');
    let html = '';

    // 整体食材分析
    html += '<div class="mt-3">';
    html += '<h6>整体食材分析（仅主材，不含调味料）</h6>';

    // 统计所有餐次的食材分类
    const allCategories = {};
    const allIngredients = new Set();

    Object.keys(data.meal_analysis).forEach(function(mealKey) {
        const meal = data.meal_analysis[mealKey];
        if (meal.has_recipes && meal.ingredient_by_category) {
            Object.keys(meal.ingredient_by_category).forEach(function(category) {
                if (!allCategories[category]) {
                    allCategories[category] = new Set();
                }
                meal.ingredient_by_category[category].forEach(function(ing) {
                    allCategories[category].add(ing.name);
                    allIngredients.add(ing.name);
                });
            });
        }
    });

    if (Object.keys(allCategories).length > 0) {
        html += '<div class="row mb-3">';
        html += '<div class="col-12">';
        html += '<div class="alert alert-info">';
        html += '<h6 class="mb-2"><i class="fas fa-chart-pie"></i> 食材分类统计</h6>';
        html += '<div class="row">';

        Object.keys(allCategories).forEach(function(category) {
            const count = allCategories[category].size;
            html += '<div class="col-md-3 mb-2">';
            html += '<span class="badge badge-primary mr-1">' + category + '</span>';
            html += '<small class="text-muted">' + count + '种</small>';
            html += '</div>';
        });

        html += '</div>';
        html += '<div class="mt-2">';
        html += '<small class="text-muted"><strong>总计:</strong> ' + allIngredients.size + '种不同的主材食材</small>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    }
    html += '</div>';

    // 餐次详情
    html += '<div class="mt-3">';
    html += '<h6>餐次详情</h6>';
    html += '<div class="row">';

    Object.keys(data.meal_analysis).forEach(function(mealKey) {
        const meal = data.meal_analysis[mealKey];

        html += '<div class="col-md-6 mb-3">';
        html += '<div class="card">';
        html += '<div class="card-header">';
        html += '<h6 class="mb-0">' + mealKey.replace('_', ' ') + '</h6>';
        html += '</div>';
        html += '<div class="card-body">';

        if (meal.has_recipes) {
            // 基本统计信息
            html += '<div class="mb-2">';
            html += '<small class="text-muted"><strong>食谱:</strong> ' + meal.recipes_count + '个</small><br>';
            html += '<small class="text-muted"><strong>主材:</strong> ' + meal.main_ingredients_count + '种（不含调味料）</small><br>';
            html += '<small class="text-muted"><strong>分类:</strong> ' + meal.category_stats.total_categories + '类</small>';
            html += '</div>';

            // 食材分类详情
            if (meal.ingredient_by_category && Object.keys(meal.ingredient_by_category).length > 0) {
                html += '<div class="ingredient-categories mb-2">';
                html += '<small class="text-muted"><strong>食材分类详情:</strong></small>';
                html += '<div class="mt-1">';

                Object.keys(meal.ingredient_by_category).forEach(function(category) {
                    const ingredients = meal.ingredient_by_category[category];
                    const uniqueIngredients = [];
                    const seenIngredients = new Set();

                    // 去重，只显示唯一的食材名称
                    ingredients.forEach(function(ing) {
                        if (!seenIngredients.has(ing.name)) {
                            seenIngredients.add(ing.name);
                            uniqueIngredients.push(ing);
                        }
                    });

                    html += '<div class="category-group">';
                    html += '<span class="badge badge-light badge-sm mr-1">' + category + ' (' + uniqueIngredients.length + ')</span>';
                    html += '<small class="text-muted">';
                    uniqueIngredients.slice(0, 3).forEach(function(ing, index) {
                        html += ing.name;
                        if (index < Math.min(2, uniqueIngredients.length - 1)) html += ', ';
                    });
                    if (uniqueIngredients.length > 3) {
                        html += '等' + uniqueIngredients.length + '种';
                    }
                    html += '</small><br>';
                    html += '</div>';
                });
                html += '</div>';
                html += '</div>';
            }

            // 智能建议
            if (meal.smart_suggestions) {
                const suggestion = meal.smart_suggestions;
                let badgeClass = 'badge-secondary';
                let iconClass = 'fas fa-info-circle';

                if (suggestion.action === 'skip') {
                    badgeClass = 'badge-danger';
                    iconClass = 'fas fa-times-circle';
                } else if (suggestion.action === 'review') {
                    badgeClass = 'badge-warning';
                    iconClass = 'fas fa-exclamation-triangle';
                } else if (suggestion.action === 'create_new') {
                    badgeClass = 'badge-success';
                    iconClass = 'fas fa-plus-circle';
                }

                html += '<div class="mt-2">';
                html += '<span class="badge ' + badgeClass + ' badge-sm"><i class="' + iconClass + '"></i> ' + suggestion.action.replace('_', ' ').toUpperCase() + '</span>';
                html += '<div class="mt-1"><small class="text-muted">' + suggestion.message + '</small></div>';
                html += '</div>';
            }

            // 相似度分析
            if (meal.similarity_analysis.has_similar) {
                html += '<div class="mt-2">';
                html += '<span class="badge badge-warning badge-sm">相似度 ' + meal.similarity_analysis.max_similarity_percent + '%</span>';
                html += '</div>';
            }

            // 已存在的采购订单
            if (meal.existing_orders && meal.existing_orders.length > 0) {
                html += '<div class="mt-2">';
                html += '<span class="badge badge-info badge-sm">已有 ' + meal.existing_orders.length + ' 个订单</span>';
                html += '</div>';
            }
        } else {
            html += '<small class="text-muted">' + meal.message + '</small>';
        }

        html += '</div>';
        html += '</div>';
        html += '</div>';
    });

    html += '</div>';
    html += '</div>';

    // 操作按钮
    html += '<div class="mt-3 text-center">';
    html += '<button type="button" class="btn btn-primary btn-lg" onclick="generatePurchaseOrder()">';
    html += '<i class="fas fa-shopping-cart"></i> 生成采购订单';
    html += '</button>';
    html += '</div>';

    analysisContent.innerHTML = html;
}

// 生成采购订单
function generatePurchaseOrder() {
    // 获取选中的餐次
    const selectedMeals = [];
    document.querySelectorAll('input[name="selected_meals"]:checked').forEach(function(checkbox) {
        selectedMeals.push(checkbox.value);
    });

    if (selectedMeals.length === 0) {
        alert('请先选择要采购的餐次');
        return;
    }

    // 发送请求生成食材清单
    fetch('/purchase-order/get-ingredients', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            area_id: areaId,
            weekly_menu_id: weeklyMenuId,
            selected_meals: selectedMeals
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示食材清单模态框
            showIngredientsModal(data.data);
        } else {
            alert('生成食材清单失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('生成食材清单失败:', error);
        alert('生成食材清单失败，请重试');
    });
}

// 显示食材清单模态框（保留原有功能）
function showIngredientsModal(data) {
    // 这里可以复用原有的模态框显示逻辑
    $('#ingredientsPreviewModal').modal('show');
    // 填充模态框内容...
}
</script>
{% endblock %}
