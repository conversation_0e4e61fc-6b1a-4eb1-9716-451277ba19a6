{% extends 'base.html' %}

{% block title %}库存管理{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* 库存管理页面样式 - 保持系统风格统一 */
.compact-toolbar {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.filter-collapse {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.btn-group-compact {
    display: flex;
    gap: 3px;
    flex-wrap: wrap;
}

.btn-xs {
    padding: 3px 8px;
    font-size: 12px;
    line-height: 1.3;
}

.table-compact {
    font-size: 15px;
}

.table-compact th {
    padding: 10px 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-weight: 600;
    font-size: 14px;
    color: white;
    border: none;
}

.table-compact td {
    padding: 10px 8px;
    vertical-align: middle;
}

.badge-sm {
    font-size: 11px;
    padding: 3px 8px;
}

.ingredient-highlight {
    font-weight: 500;
    color: #495057;
}

.ingredient-category {
    color: #6c757d;
    font-size: 12px;
}

.stock-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.stock-indicator.sufficient { background-color: #28a745; }
.stock-indicator.low { background-color: #ffc107; }
.stock-indicator.critical { background-color: #17a2b8; }
.stock-indicator.expired { background-color: #dc3545; }

/* 食材流转状态样式 */
.inventory-row.fully-consumed {
    background-color: #f8f9fa !important;
    color: #6c757d;
    opacity: 0.7;
}

.inventory-row.fully-consumed td {
    border-color: #e9ecef;
}

.flow-status {
    font-size: 12px;
    margin-top: 3px;
}

.flow-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;
}

.flow-indicator.available { background-color: #28a745; }
.flow-indicator.partially-consumed { background-color: #ffc107; }
.flow-indicator.fully-consumed { background-color: #6c757d; }
.flow-indicator.empty { background-color: #dc3545; }

/* 专业化表格样式 */
.ingredient-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 17px;
    line-height: 1.2;
    margin-bottom: 2px;
}

.ingredient-name a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.2s ease;
}

.ingredient-name a:hover {
    color: #3498db;
    text-decoration: none;
}

.ingredient-category {
    color: #dc3545;
    font-size: 13px;
    font-weight: 500;
    display: inline-block;
    margin-left: 8px;
}

.batch-number {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 13px;
    color: #495057;
    border: 1px solid #e9ecef;
}

.quantity-display {
    font-weight: 600;
    font-size: 16px;
    color: #2980b9;
}

.unit-display {
    color: #7f8c8d;
    font-size: 13px;
}

.date-display {
    font-size: 13px;
    color: #495057;
}

.storage-location {
    font-weight: 500;
    color: #34495e;
}

.location-code {
    color: #95a5a6;
    font-size: 11px;
    font-weight: 400;
}

.status-cell {
    text-align: center;
}

.action-cell {
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .compact-toolbar {
        padding: 10px 15px;
    }

    .table-compact {
        font-size: 14px;
    }

    .table-compact th,
    .table-compact td {
        padding: 8px 6px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">

    <!-- 精简的库存管理工具栏 -->
    <div class="compact-toolbar d-flex justify-content-between align-items-center">
        <!-- 左侧：页面标题 -->
        <div class="d-flex align-items-center">
            <h5 class="mb-0 mr-3"><i class="fas fa-boxes mr-2"></i>库存管理</h5>
            <div class="btn-group btn-group-sm">
                <a href="{{ url_for('inventory.index', view_type='detail') }}"
                   class="btn {% if view_type == 'detail' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                    <i class="fas fa-list"></i> 详细
                </a>
                <a href="{{ url_for('inventory.index', view_type='summary') }}"
                   class="btn {% if view_type == 'summary' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                    <i class="fas fa-chart-pie"></i> 汇总
                </a>
            </div>
        </div>

        <!-- 右侧：快捷操作 -->
        <div class="d-flex align-items-center">
            <a href="{{ url_for('inventory.check_expiry') }}" class="btn btn-warning btn-sm mr-2">
                <i class="fas fa-exclamation-triangle"></i> 临期检查
            </a>
            <a href="{{ url_for('stock_out.index') }}" class="btn btn-outline-secondary btn-sm mr-2">
                <i class="fas fa-sign-out-alt"></i> 出库
            </a>
            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-calendar-alt"></i> 菜单计划
            </a>
            <button type="button" class="btn btn-outline-secondary btn-sm ml-2" data-toggle="collapse" data-target="#filterForm">
                <i class="fas fa-filter"></i> 筛选
            </button>
        </div>
    </div>

    <!-- 可折叠的筛选区域 -->
    <div class="collapse mb-3" id="filterForm">
        <div class="filter-collapse">
            <form method="get" action="{{ url_for('inventory.index') }}">
                <input type="hidden" name="view_type" value="{{ view_type }}">
                <div class="row">
                    <div class="col-md-2">
                        <select name="warehouse_id" class="form-control form-control-sm" id="warehouse_id" onchange="loadStorageLocations()">
                            <option value="">全部仓库</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id %}selected{% endif %}>{{ warehouse.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="storage_location_id" class="form-control form-control-sm" id="storage_location_id">
                            <option value="">全部位置</option>
                            {% for location in storage_locations %}
                            <option value="{{ location.id }}" {% if storage_location_id == location.id %}selected{% endif %}>{{ location.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="ingredient_id" class="form-control form-control-sm">
                            <option value="">全部食材</option>
                            {% for ingredient in ingredients %}
                            <option value="{{ ingredient.id }}" {% if ingredient_id == ingredient.id %}selected{% endif %}>{{ ingredient.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-control form-control-sm">
                            <option value="">全部状态</option>
                            <option value="正常" {% if status == '正常' %}selected{% endif %}>正常</option>
                            <option value="待检" {% if status == '待检' %}selected{% endif %}>待检</option>
                            <option value="冻结" {% if status == '冻结' %}selected{% endif %}>冻结</option>
                            <option value="已过期" {% if status == '已过期' %}selected{% endif %}>已过期</option>
                            <option value="已用完" {% if status == '已用完' %}selected{% endif %}>已用完</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="expiry_days" class="form-control form-control-sm">
                            <option value="">全部期限</option>
                            <option value="7" {% if expiry_days == 7 %}selected{% endif %}>7天内过期</option>
                            <option value="15" {% if expiry_days == 15 %}selected{% endif %}>15天内过期</option>
                            <option value="30" {% if expiry_days == 30 %}selected{% endif %}>30天内过期</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary btn-sm mr-1">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i> 清除
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 直接显示表格，无外层卡片 -->
    <div class="table-responsive">
        <table class="table table-compact table-hover table-bordered">
            <thead>
                <tr>
                    <th style="width: 18%;">🥬 食材名称</th>
                    <th style="width: 15%;">存储位置</th>
                    <th style="width: 12%;">批次号</th>
                    <th style="width: 10%;">数量</th>
                    <th style="width: 8%;">单位</th>
                    <th style="width: 12%;">生产日期</th>
                    <th style="width: 12%;">过期日期</th>
                    <th style="width: 8%;">状态</th>
                    <th style="width: 5%;">操作</th>
                </tr>
            </thead>
                        <tbody>
                            {% for inventory in inventories %}
                            <tr class="inventory-row {% if inventory.flow_status.status == 'fully_consumed' %}fully-consumed{% endif %}">
                                <td>
                                    <div class="ingredient-name">
                                        <a href="{{ url_for('ingredient.turnover', id=inventory.ingredient.id) }}" title="查看食材周转情况">
                                            {{ inventory.ingredient.name }}
                                        </a>
                                        {% if inventory.ingredient.category %}
                                        <span class="ingredient-category">{{ inventory.ingredient.category.name }}</span>
                                        {% endif %}
                                    </div>
                                    <div class="flow-status">
                                        <span class="flow-indicator {{ inventory.flow_status.status }}"></span>
                                        <small class="text-{{ inventory.flow_status.class }}">{{ inventory.flow_status.label }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="storage-location">{{ inventory.storage_location.name }}</div>
                                    <div class="location-code">({{ inventory.storage_location.location_code }})</div>
                                </td>
                                <td>
                                    <span class="batch-number">{{ inventory.batch_number }}</span>
                                </td>
                                <td class="text-right">
                                    <div class="quantity-display">{{ inventory.quantity }}</div>
                                </td>
                                <td>
                                    <span class="unit-display">{{ inventory.unit }}</span>
                                </td>
                                <td>
                                    <div class="date-display">{{ inventory.production_date|format_datetime('%m-%d') }}</div>
                                </td>
                                <td>
                                    <div class="date-display">{{ inventory.expiry_date|format_datetime('%m-%d') }}</div>
                                    {% if inventory.status == '已过期' %}
                                        <div><span class="badge badge-danger badge-sm">已过期</span></div>
                                    {% elif inventory.status == '临期' %}
                                        <div><span class="badge badge-warning badge-sm">临期</span></div>
                                    {% endif %}
                                </td>
                                <td class="status-cell">
                                    {% if inventory.status == '正常' %}
                                        <span class="stock-indicator sufficient"></span>
                                        <span class="badge badge-success badge-sm">正常</span>
                                    {% elif inventory.status == '待检' %}
                                        <span class="stock-indicator low"></span>
                                        <span class="badge badge-warning badge-sm">待检</span>
                                    {% elif inventory.status == '冻结' %}
                                        <span class="stock-indicator critical"></span>
                                        <span class="badge badge-info badge-sm">冻结</span>
                                    {% elif inventory.status == '已过期' %}
                                        <span class="stock-indicator expired"></span>
                                        <span class="badge badge-danger badge-sm">已过期</span>
                                    {% elif inventory.status == '已用完' %}
                                        <span class="stock-indicator expired"></span>
                                        <span class="badge badge-secondary badge-sm">已用完</span>
                                    {% endif %}
                                </td>
                                <td class="action-cell">
                                    <a href="{{ url_for('inventory.detail', id=inventory.id) }}"
                                       class="btn btn-xs btn-outline-info" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <i class="fas fa-box-open text-muted"></i>
                                    <br><small class="text-muted">暂无库存数据</small>
                                </td>
                            </tr>
                            {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 精简分页 -->
    {% if pagination.pages > 1 %}
    <div class="d-flex justify-content-center mt-3">
        <ul class="pagination pagination-sm">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('inventory.index', page=pagination.prev_num, warehouse_id=warehouse_id, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">
                    «
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">«</span>
            </li>
            {% endif %}

            {% for page in pagination.iter_pages() %}
                {% if page %}
                    {% if page != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.index', page=page, warehouse_id=warehouse_id, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('inventory.index', page=pagination.next_num, warehouse_id=warehouse_id, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">
                    »
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">»</span>
            </li>
            {% endif %}
        </ul>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 加载存储位置
    function loadStorageLocations() {
        const warehouseId = document.getElementById('warehouse_id').value;
        const storageLocationSelect = document.getElementById('storage_location_id');

        // 清空现有选项
        storageLocationSelect.innerHTML = '<option value="">全部</option>';

        if (!warehouseId) return;

        // 发送AJAX请求获取存储位置
        fetch(`{{ url_for('inventory.get_storage_locations') }}?warehouse_id=${warehouseId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    storageLocationSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading storage locations:', error));
    }
</script>
{% endblock %}
