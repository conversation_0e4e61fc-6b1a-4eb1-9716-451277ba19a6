"""
食材溯源路由模块
基于入库、消耗计划、出库与周菜谱菜品、食材关联的完整溯源体系
实现从供应商到餐桌的全链路追踪，严格按学校级数据隔离
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, jsonify, abort
from flask_login import login_required, current_user
from app.models import (
    WeeklyMenu, WeeklyMenuRecipe, MenuPlan, MenuRecipe, Recipe, RecipeIngredient,
    ConsumptionPlan, ConsumptionDetail, StockOut, StockOutItem, Inventory,
    StockIn, StockInItem, PurchaseOrder, PurchaseOrderItem, Supplier, Ingredient,
    FoodSample, AdministrativeArea, Warehouse
)
from app.utils.school_required import school_required
from datetime import datetime, date, timedelta
from sqlalchemy import and_, or_

food_trace_bp = Blueprint('food_trace', __name__)

@food_trace_bp.route('/food-trace')
@login_required
@school_required
def index(user_area):
    """
    食材溯源首页 - 基于完整的学校级数据隔离
    支持通过消耗计划ID、出库单ID或日期餐次进行溯源查询
    """
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    trace_date = request.args.get('date')
    meal_type = request.args.get('meal_type')
    consumption_plan_id = request.args.get('consumption_plan_id', type=int)
    stock_out_id = request.args.get('stock_out_id', type=int)

    # 验证区域权限
    if area_id and area_id not in area_ids:
        flash('您没有权限访问该学校的数据', 'danger')
        area_id = None

    # 默认使用用户当前学校
    if not area_id and accessible_areas:
        area_id = accessible_areas[0].id

    # 默认参数
    if not trace_date:
        trace_date = date.today().strftime('%Y-%m-%d')
    if not meal_type:
        meal_type = '午餐'

    trace_data = None
    error_message = None

    try:
        # 优先级1：通过出库单ID溯源
        if stock_out_id:
            trace_data = trace_by_stock_out(stock_out_id, area_ids)
            if trace_data:
                # 更新查询参数以反映实际溯源的数据
                if trace_data.get('consumption_plan'):
                    plan = trace_data['consumption_plan']
                    trace_date = plan['consumption_date']
                    meal_type = plan['meal_type']
                    area_id = plan.get('area_id') or area_id

        # 优先级2：通过消耗计划ID溯源
        elif consumption_plan_id:
            trace_data = trace_by_consumption_plan(consumption_plan_id, area_ids)
            if trace_data:
                # 更新查询参数
                if trace_data.get('consumption_plan'):
                    plan = trace_data['consumption_plan']
                    trace_date = plan['consumption_date']
                    meal_type = plan['meal_type']
                    area_id = plan.get('area_id') or area_id

        # 优先级3：通过日期、餐次、学校溯源
        elif area_id and trace_date and meal_type:
            trace_data = trace_by_date_meal_area(trace_date, meal_type, area_id)

    except Exception as e:
        current_app.logger.error(f"食材溯源失败: {str(e)}")
        error_message = f"溯源查询失败: {str(e)}"

    # 检查是否有菜品安排
    has_recipes = False
    no_recipes_message = None

    if area_id and trace_date and meal_type and not error_message:
        if trace_data and trace_data.get('recipes') and len(trace_data['recipes']) > 0:
            has_recipes = True
        else:
            # 查询指定日期餐次是否有菜品安排
            area_name = next((area.name for area in accessible_areas if area.id == area_id), '未知区域')
            no_recipes_message = f"{area_name} {trace_date} {meal_type} 无菜品安排"

    return render_template('food_trace/index.html',
                          areas=accessible_areas,
                          area_id=area_id,
                          trace_date=trace_date,
                          meal_type=meal_type,
                          trace_data=trace_data,
                          error_message=error_message,
                          consumption_plan_id=consumption_plan_id,
                          stock_out_id=stock_out_id,
                          has_recipes=has_recipes,
                          no_recipes_message=no_recipes_message)

def trace_by_stock_out(stock_out_id, area_ids):
    """
    通过出库单ID进行溯源
    基于我们的完整溯源架构：出库单 → 消耗计划 → 周菜单 → 食谱 → 食材 → 供应商
    """
    try:
        # 1. 获取出库单信息（验证权限）
        stock_out = StockOut.query.join(Warehouse).filter(
            StockOut.id == stock_out_id,
            Warehouse.area_id.in_(area_ids)
        ).first()

        if not stock_out:
            current_app.logger.warning(f"出库单 {stock_out_id} 不存在或无权限访问")
            return None

        current_app.logger.info(f"开始溯源出库单: {stock_out.stock_out_number}")

        # 2. 获取学校区域ID
        area_id = stock_out.warehouse.area_id

        # 3. 获取消耗计划信息
        consumption_plan = None
        if stock_out.consumption_plan_id:
            consumption_plan = ConsumptionPlan.query.get(stock_out.consumption_plan_id)

        # 4. 获取菜谱信息
        recipes_info = []
        if consumption_plan:
            consumption_date = consumption_plan.consumption_date
            meal_type = consumption_plan.meal_type

            # 通过消耗计划获取菜谱
            recipes_info = get_recipes_by_date_meal_area(consumption_date, meal_type, area_id)

        # 5. 获取出库明细和批次信息
        stock_out_items = StockOutItem.query.filter_by(stock_out_id=stock_out_id).all()

        # 6. 构建完整溯源链
        trace_data = {
            'stock_out': {
                'id': stock_out.id,
                'stock_out_number': stock_out.stock_out_number,
                'warehouse_name': stock_out.warehouse.name,
                'stock_out_date': stock_out.stock_out_date.strftime('%Y-%m-%d'),
                'stock_out_type': stock_out.stock_out_type,
                'operator_name': stock_out.operator.real_name or stock_out.operator.username if stock_out.operator else None,
                'status': stock_out.status
            },
            'consumption_plan': {
                'id': consumption_plan.id if consumption_plan else None,
                'consumption_date': consumption_plan.consumption_date.strftime('%Y-%m-%d') if consumption_plan else None,
                'meal_type': consumption_plan.meal_type if consumption_plan else None,
                'diners_count': consumption_plan.diners_count if consumption_plan else None,
                'area_id': area_id
            } if consumption_plan else None,
            'recipes': recipes_info,
            'ingredients': [],
            'batches': [],
            'suppliers': []
        }

        # 7. 获取食材批次和供应商信息
        for item in stock_out_items:
            # 批次信息
            batch_info = {
                'ingredient_name': item.ingredient.name if item.ingredient else None,
                'batch_number': item.batch_number,
                'quantity': float(item.quantity),
                'unit': item.unit,
                'expiry_date': item.inventory.expiry_date.strftime('%Y-%m-%d') if item.inventory and item.inventory.expiry_date else None
            }

            # 获取供应商信息
            supplier_info = get_supplier_by_batch(item.batch_number)
            if supplier_info:
                batch_info['supplier'] = supplier_info

            trace_data['batches'].append(batch_info)

        # 8. 进行食材比对分析
        ingredient_analysis = analyze_ingredient_consistency(recipes_info, trace_data['batches'])
        trace_data['ingredient_analysis'] = ingredient_analysis

        return trace_data

    except Exception as e:
        current_app.logger.error(f"通过出库单溯源失败: {str(e)}")
        return None

def trace_by_consumption_plan(consumption_plan_id, area_ids):
    """
    通过消耗计划ID进行溯源
    """
    try:
        # 1. 获取消耗计划（验证权限）
        consumption_plan = ConsumptionPlan.query.filter(
            ConsumptionPlan.id == consumption_plan_id,
            ConsumptionPlan.area_id.in_(area_ids)
        ).first()

        if not consumption_plan:
            current_app.logger.warning(f"消耗计划 {consumption_plan_id} 不存在或无权限访问")
            return None

        # 2. 获取关联的出库单
        stock_outs = StockOut.query.filter_by(consumption_plan_id=consumption_plan_id).all()

        # 3. 如果有出库单，使用第一个出库单进行溯源
        if stock_outs:
            return trace_by_stock_out(stock_outs[0].id, area_ids)

        # 4. 如果没有出库单，基于消耗计划信息构建溯源数据
        area_id = consumption_plan.area_id
        recipes_info = get_recipes_by_date_meal_area(
            consumption_plan.consumption_date,
            consumption_plan.meal_type,
            area_id
        )

        trace_data = {
            'consumption_plan': {
                'id': consumption_plan.id,
                'consumption_date': consumption_plan.consumption_date.strftime('%Y-%m-%d'),
                'meal_type': consumption_plan.meal_type,
                'diners_count': consumption_plan.diners_count,
                'area_id': area_id
            },
            'recipes': recipes_info,
            'stock_out': None,
            'ingredients': [],
            'batches': [],
            'suppliers': []
        }

        # 添加食材分析（基于空的批次信息）
        ingredient_analysis = analyze_ingredient_consistency(recipes_info, [])
        trace_data['ingredient_analysis'] = ingredient_analysis

        return trace_data

    except Exception as e:
        current_app.logger.error(f"通过消耗计划溯源失败: {str(e)}")
        return None

def trace_by_date_meal_area(trace_date, meal_type, area_id):
    """
    通过日期、餐次、学校进行溯源
    """
    try:
        trace_date_obj = datetime.strptime(trace_date, '%Y-%m-%d').date()

        # 1. 获取菜谱信息
        recipes_info = get_recipes_by_date_meal_area(trace_date_obj, meal_type, area_id)

        # 2. 查找消耗计划
        consumption_plan = ConsumptionPlan.query.filter(
            ConsumptionPlan.consumption_date == trace_date_obj,
            ConsumptionPlan.meal_type == meal_type,
            ConsumptionPlan.area_id == area_id
        ).first()

        # 3. 如果有消耗计划，查找关联的出库单
        stock_out = None
        if consumption_plan:
            stock_out = StockOut.query.filter_by(consumption_plan_id=consumption_plan.id).first()

        # 4. 如果有出库单，使用出库单溯源
        if stock_out:
            return trace_by_stock_out(stock_out.id, [area_id])

        # 5. 构建基础溯源数据
        trace_data = {
            'consumption_plan': {
                'id': consumption_plan.id if consumption_plan else None,
                'consumption_date': trace_date,
                'meal_type': meal_type,
                'diners_count': consumption_plan.diners_count if consumption_plan else None,
                'area_id': area_id
            },
            'recipes': recipes_info,
            'stock_out': None,
            'ingredients': [],
            'batches': [],
            'suppliers': []
        }

        # 添加食材分析（基于空的批次信息）
        ingredient_analysis = analyze_ingredient_consistency(recipes_info, [])
        trace_data['ingredient_analysis'] = ingredient_analysis

        return trace_data

    except Exception as e:
        current_app.logger.error(f"通过日期餐次溯源失败: {str(e)}")
        return None

def get_recipes_by_date_meal_area(trace_date, meal_type, area_id):
    """
    根据日期、餐次、学校获取菜谱信息
    基于我们的周菜单架构
    """
    try:
        recipes_info = []

        # 1. 计算星期几
        weekday = trace_date.weekday()  # 0-6，0表示周一
        day_of_week = weekday + 1  # 数据库中1-7表示周一到周日

        current_app.logger.info(f"查询菜谱：日期={trace_date}, 星期={weekday}(0=周一), day_of_week={day_of_week}, 餐次={meal_type}, 区域ID={area_id}")

        # 2. 查找周菜单
        weekly_menus = WeeklyMenu.query.filter(
            WeeklyMenu.week_start <= trace_date,
            WeeklyMenu.week_end >= trace_date,
            WeeklyMenu.area_id == area_id,
            WeeklyMenu.status == '已发布'
        ).all()

        current_app.logger.info(f"找到 {len(weekly_menus)} 个周菜单")

        if weekly_menus:
            weekly_menu = weekly_menus[0]

            # 3. 获取当日餐次的食谱
            weekly_recipes = WeeklyMenuRecipe.query.filter(
                WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                WeeklyMenuRecipe.day_of_week == day_of_week,
                WeeklyMenuRecipe.meal_type == meal_type
            ).all()

            current_app.logger.info(f"匹配条件的食谱有 {len(weekly_recipes)} 个")

            for weekly_recipe in weekly_recipes:
                if weekly_recipe.recipe:
                    # 获取食谱的主要食材（排除调味品）
                    main_ingredients = get_recipe_main_ingredients(weekly_recipe.recipe.id)

                    recipe_info = {
                        'id': weekly_recipe.recipe.id,
                        'name': weekly_recipe.recipe.name,
                        'category': weekly_recipe.recipe.category,
                        'main_ingredients': main_ingredients,
                        'source': 'weekly_menu'
                    }
                    recipes_info.append(recipe_info)
                    current_app.logger.info(f"  - 食谱: {weekly_recipe.recipe.name}")

        # 4. 如果周菜单没有找到，尝试查找日菜单计划
        if not recipes_info:
            menu_plan = MenuPlan.query.filter(
                MenuPlan.plan_date == trace_date,
                MenuPlan.meal_type == meal_type,
                MenuPlan.area_id == area_id,
                MenuPlan.status.in_(['已发布', '已执行'])
            ).first()

            if menu_plan:
                menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=menu_plan.id).all()
                for menu_recipe in menu_recipes:
                    if menu_recipe.recipe:
                        main_ingredients = get_recipe_main_ingredients(menu_recipe.recipe.id)

                        recipe_info = {
                            'id': menu_recipe.recipe.id,
                            'name': menu_recipe.recipe.name,
                            'category': menu_recipe.recipe.category,
                            'main_ingredients': main_ingredients,
                            'source': 'menu_plan',
                            'planned_quantity': menu_recipe.planned_quantity,
                            'actual_quantity': menu_recipe.actual_quantity
                        }
                        recipes_info.append(recipe_info)

        return recipes_info

    except Exception as e:
        current_app.logger.error(f"获取菜谱信息失败: {str(e)}")
        return []

def get_recipe_main_ingredients(recipe_id):
    """
    获取食谱的主要食材（排除调味品）
    """
    try:
        # 定义调味品关键词
        seasoning_keywords = ['盐', '糖', '醋', '酱油', '料酒', '胡椒', '花椒', '八角', '桂皮', '香叶',
                             '生抽', '老抽', '蚝油', '味精', '鸡精', '十三香', '五香粉', '孜然', '辣椒粉',
                             '芝麻油', '香油', '调料', '味料', '香料', '佐料']

        recipe_ingredients = RecipeIngredient.query.filter_by(recipe_id=recipe_id).all()

        main_ingredients = []
        for ri in recipe_ingredients:
            if ri.ingredient:
                ingredient_name = ri.ingredient.name
                # 检查是否为调味品
                is_seasoning = any(keyword in ingredient_name for keyword in seasoning_keywords)

                # 也可以通过食材分类判断
                if ri.ingredient.category:
                    if isinstance(ri.ingredient.category, str):
                        category_name = ri.ingredient.category
                    else:
                        category_name = ri.ingredient.category.name if hasattr(ri.ingredient.category, 'name') else str(ri.ingredient.category)

                    if '调味品' in category_name or '香料' in category_name or '佐料' in category_name:
                        is_seasoning = True

                if not is_seasoning:
                    main_ingredients.append(ingredient_name)

        return main_ingredients[:5]  # 最多返回5个主要食材

    except Exception as e:
        current_app.logger.error(f"获取食谱主要食材失败: {str(e)}")
        return []

def get_supplier_by_batch(batch_number):
    """
    根据批次号获取供应商信息
    """
    try:
        if not batch_number:
            return None

        # 通过入库明细查找供应商
        stock_in_item = StockInItem.query.filter_by(batch_number=batch_number).first()
        if stock_in_item and stock_in_item.supplier:
            return {
                'id': stock_in_item.supplier.id,
                'name': stock_in_item.supplier.name,
                'contact_person': stock_in_item.supplier.contact_person,
                'phone': stock_in_item.supplier.phone,
                'address': stock_in_item.supplier.address
            }

        # 如果入库明细没有供应商，通过入库单查找
        if stock_in_item and stock_in_item.stock_in and stock_in_item.stock_in.supplier:
            supplier = stock_in_item.stock_in.supplier
            return {
                'id': supplier.id,
                'name': supplier.name,
                'contact_person': supplier.contact_person,
                'phone': supplier.phone,
                'address': supplier.address
            }

        return None

    except Exception as e:
        current_app.logger.error(f"获取供应商信息失败: {str(e)}")
        return None

def analyze_ingredient_consistency(recipes_info, batches_info):
    """
    分析出库食材与食谱所需食材的一致性
    """
    try:
        analysis = {
            'has_discrepancy': False,
            'missing_ingredients': [],  # 食谱需要但未出库的食材
            'extra_ingredients': [],    # 出库了但食谱不需要的食材
            'matched_ingredients': [],  # 匹配的食材
            'recipe_ingredients': [],   # 所有食谱食材
            'stock_out_ingredients': [] # 所有出库食材
        }

        # 1. 收集所有食谱需要的食材
        recipe_ingredients_set = set()
        for recipe in recipes_info:
            if recipe.get('main_ingredients'):
                for ingredient in recipe['main_ingredients']:
                    recipe_ingredients_set.add(ingredient.strip())
                    analysis['recipe_ingredients'].append({
                        'name': ingredient.strip(),
                        'recipe': recipe['name']
                    })

        # 2. 收集所有出库的食材
        stock_out_ingredients_set = set()
        for batch in batches_info:
            if batch.get('ingredient_name'):
                ingredient_name = batch['ingredient_name'].strip()
                stock_out_ingredients_set.add(ingredient_name)
                analysis['stock_out_ingredients'].append({
                    'name': ingredient_name,
                    'quantity': batch.get('quantity', 0),
                    'unit': batch.get('unit', ''),
                    'batch_number': batch.get('batch_number', '')
                })

        # 3. 进行比对分析
        # 找出食谱需要但未出库的食材
        missing = recipe_ingredients_set - stock_out_ingredients_set
        for ingredient in missing:
            analysis['missing_ingredients'].append(ingredient)

        # 找出出库了但食谱不需要的食材
        extra = stock_out_ingredients_set - recipe_ingredients_set
        for ingredient in extra:
            analysis['extra_ingredients'].append(ingredient)

        # 找出匹配的食材
        matched = recipe_ingredients_set & stock_out_ingredients_set
        for ingredient in matched:
            analysis['matched_ingredients'].append(ingredient)

        # 4. 判断是否存在差异
        if analysis['missing_ingredients'] or analysis['extra_ingredients']:
            analysis['has_discrepancy'] = True

        # 5. 生成分析报告
        total_recipe_ingredients = len(recipe_ingredients_set)
        total_matched = len(matched)

        if total_recipe_ingredients > 0:
            match_rate = (total_matched / total_recipe_ingredients) * 100
            analysis['match_rate'] = round(match_rate, 1)
        else:
            analysis['match_rate'] = 0

        current_app.logger.info(f"食材一致性分析完成: 匹配率={analysis['match_rate']}%, 缺失={len(analysis['missing_ingredients'])}, 多余={len(analysis['extra_ingredients'])}")

        return analysis

    except Exception as e:
        current_app.logger.error(f"食材一致性分析失败: {str(e)}")
        return {
            'has_discrepancy': False,
            'missing_ingredients': [],
            'extra_ingredients': [],
            'matched_ingredients': [],
            'recipe_ingredients': [],
            'stock_out_ingredients': [],
            'match_rate': 0
        }

@food_trace_bp.route('/food-trace/get-recipes', methods=['GET'])
@login_required
def get_recipes():
    """
    获取指定区域、日期和餐次的菜谱列表。
    用于前端动态加载菜谱信息。
    """
    area_id = request.args.get('area_id', type=int)
    query_date_str = request.args.get('date')
    meal_type = request.args.get('meal_type')

    if not area_id or not query_date_str or not meal_type:
        return jsonify({'success': False, 'message': '缺少area_id, date或meal_type参数'}), 400

    try:
        query_date = datetime.strptime(query_date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'success': False, 'message': '日期格式不正确，应为YYYY-MM-DD'}), 400

    # 检查用户是否有权限访问该区域
    if not current_user.can_access_area_by_id(area_id):
         return jsonify({'success': False, 'message': '您没有权限访问该区域的数据'}), 403

    # 查找指定区域、日期和餐次的菜单计划
    menu_plan = MenuPlan.query.filter(
        MenuPlan.area_id == area_id,
        MenuPlan.plan_date == query_date,
        MenuPlan.meal_type == meal_type,
        MenuPlan.status.in_(['已发布', '已执行'])
    ).first()

    recipes_list = []
    if menu_plan:
        # 获取菜单计划关联的菜谱
        menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=menu_plan.id).all()
        for menu_recipe in menu_recipes:
            if menu_recipe.recipe:
                recipes_list.append({
                    'id': menu_recipe.recipe.id,
                    'name': menu_recipe.recipe.name,
                    'notes': menu_recipe.recipe.notes,
                    # 可以添加其他需要的菜谱信息
                })
    # else:
        # 如果没有找到菜单计划，也可以选择从周菜单查找，但这会增加复杂性，暂不实现，只返回空列表

    return jsonify({'success': True, 'data': recipes_list})

@food_trace_bp.route('/food-trace/sample-management')
@login_required
def sample_management():
    """留样管理页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    trace_date = request.args.get('date')
    meal_type = request.args.get('meal_type')

    # 默认为今天
    if not trace_date:
        trace_date = date.today().strftime('%Y-%m-%d')

    # 默认为午餐
    if not meal_type:
        meal_type = '午餐'

    # 如果没有指定区域，使用用户的第一个可访问区域
    if not area_id and accessible_areas:
        area_id = accessible_areas[0].id

    return render_template('food_trace/sample_management.html',
                          areas=accessible_areas,
                          area_id=area_id,
                          today=date.today().strftime('%Y-%m-%d'))

@food_trace_bp.route('/food-trace/one-click-sample')
@login_required
def one_click_sample():
    """一键留样功能，自动创建今日工作日志并跳转到留样管理页面"""
    try:
        # 获取当前用户所属学校/区域
        user_area = current_user.get_current_area()
        if not user_area:
            flash('您没有关联到任何学校，无法使用一键留样功能', 'danger')
            return redirect(url_for('main.index'))

        # 准备今日日志数据
        today_date = date.today()
        data = {
            'log_date': today_date.strftime('%Y-%m-%d'),
            'area_id': user_area.id,
            'manager': current_user.real_name or current_user.username,
            'student_count': 0,
            'teacher_count': 0,
            'other_count': 0,
            'created_by': current_user.id
        }

        # 注释掉日志服务，专注于溯源功能
        # log = DailyLogService.create_daily_log(data)
        # if log:
        #     flash(f'已自动创建或获取{user_area.name}今日工作日志', 'info')
        # else:
        #     flash('无法创建或获取今日工作日志', 'warning')

        # 重定向到留样管理页面
        return redirect(url_for('food_trace.sample_management', area_id=user_area.id))
    except Exception as e:
        flash(f'创建工作日志失败: {str(e)}', 'danger')
        return redirect(url_for('main.index'))

@food_trace_bp.route('/food-trace/print-samples')
@login_required
def print_samples():
    """打印留样记录"""
    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    trace_date = request.args.get('date')
    meal_type = request.args.get('meal_type')
    recipe_ids = request.args.get('recipes')

    # 留样设置参数
    sample_quantity = request.args.get('quantity', 50, type=float)
    storage_hours = request.args.get('hours', 48, type=int)
    storage_location = request.args.get('location', '留样冰箱')
    storage_temp = request.args.get('temp', -18, type=float)

    # 默认为今天
    if not trace_date:
        trace_date = date.today().strftime('%Y-%m-%d')

    # 默认为午餐
    if not meal_type:
        meal_type = '午餐'

    # 获取区域信息
    area = None
    if area_id:
        area = AdministrativeArea.query.get(area_id)

        # 检查用户权限
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限查看该区域的数据', 'danger')
            return redirect(url_for('food_trace.index'))

    # 将字符串日期转换为日期对象
    sample_date = datetime.strptime(trace_date, '%Y-%m-%d').date()

    # 处理指定的菜谱ID
    selected_recipe_ids = []
    if recipe_ids:
        selected_recipe_ids = [int(id) for id in recipe_ids.split(',')]

    # 获取菜单信息
    menu_plan = None
    all_recipes = []

    menu_plan_query = MenuPlan.query.filter(
        MenuPlan.plan_date == sample_date,
        MenuPlan.meal_type == meal_type,
        MenuPlan.status.in_(['已发布', '已执行'])
    )

    if area_id:
        menu_plan_query = menu_plan_query.filter(MenuPlan.area_id == area_id)

    menu_plan = menu_plan_query.first()

    if menu_plan:
        menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=menu_plan.id).all()
        for menu_recipe in menu_recipes:
            if menu_recipe.recipe:
                all_recipes.append(menu_recipe.recipe)
    else:
        # 如果没有找到菜单计划，尝试从周菜单中查找
        weekday = sample_date.weekday()  # 0-6，0表示周一

        weekly_menu_query = WeeklyMenu.query.filter(
            WeeklyMenu.week_start <= sample_date,
            WeeklyMenu.week_end >= sample_date,
            WeeklyMenu.status == '已发布'
        )

        if area_id:
            weekly_menu_query = weekly_menu_query.filter(WeeklyMenu.area_id == area_id)

        weekly_menu = weekly_menu_query.first()

        if weekly_menu:
            weekly_recipes = WeeklyMenuRecipe.query.filter(
                WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
                WeeklyMenuRecipe.meal_type == meal_type
            ).all()

            for weekly_recipe in weekly_recipes:
                if weekly_recipe.recipe:
                    all_recipes.append(weekly_recipe.recipe)

    # 如果指定了菜谱ID，则只处理这些菜谱
    if selected_recipe_ids:
        recipes_to_print = [recipe for recipe in all_recipes if recipe.id in selected_recipe_ids]
    else:
        recipes_to_print = all_recipes

    # 获取留样记录
    food_samples = []
    if area_id:
        food_samples = FoodSample.query.filter(
            FoodSample.meal_date == sample_date,
            FoodSample.meal_type == meal_type,
            FoodSample.area_id == area_id
        ).all()

    # 将菜谱和留样记录关联起来，并获取主要食材信息
    recipes_with_samples = []
    for recipe in recipes_to_print:
        # 获取食谱的主要食材（最多3种）
        main_ingredients = []
        recipe_ingredients = RecipeIngredient.query.filter_by(recipe_id=recipe.id).all()
        for ri in recipe_ingredients[:3]:  # 最多取前3种食材
            if ri.ingredient:
                main_ingredients.append(ri.ingredient.name)

        recipe_data = {
            'recipe': recipe,
            'sample': None,
            'main_ingredients': ', '.join(main_ingredients) if main_ingredients else '无食材信息'
        }

        # 查找对应的留样记录
        for sample in food_samples:
            if sample.recipe_id == recipe.id:
                recipe_data['sample'] = sample
                break

        recipes_with_samples.append(recipe_data)

    # 计算留样时间和销毁时间
    current_time = datetime.now()
    end_time = current_time + timedelta(hours=storage_hours)

    return render_template('food_trace/print_samples.html',
                          area=area,
                          trace_date=trace_date,
                          meal_type=meal_type,
                          menu_plan=menu_plan,
                          recipes_with_samples=recipes_with_samples,
                          current_time=current_time,
                          end_time=end_time,
                          sample_quantity=sample_quantity,
                          storage_location=storage_location,
                          storage_temp=storage_temp)

@food_trace_bp.route('/food-trace/view/<int:area_id>/<string:date_str>/<string:meal_type>')
@login_required
def view_trace(area_id, date_str, meal_type):
    """查看指定日期和餐次的溯源信息"""
    # 检查用户权限
    if not current_user.can_access_area_by_id(area_id):
        flash('您没有权限查看该区域的数据', 'danger')
        return redirect(url_for('food_trace.index'))

    # 获取区域信息
    area = AdministrativeArea.query.get_or_404(area_id)

    # 获取溯源数据
    trace_data = trace_by_date_meal_area(date_str, meal_type, area_id)

    # 检查是否有菜品安排
    has_recipes = trace_data and trace_data.get('recipes') and len(trace_data['recipes']) > 0

    if not trace_data:
        flash('未找到溯源数据', 'danger')
        return redirect(url_for('food_trace.index'))

    return render_template('food_trace/view.html',
                          area=area,
                          trace_date=date_str,
                          meal_type=meal_type,
                          trace_data=trace_data)
